{"total_functions": 80, "tested_count": 10, "documented_count": 15, "test_coverage": 12.5, "doc_coverage": 18.75, "complete_coverage": 12.5, "coverage_details": [{"id": 1, "name": "数据看板", "category": "核心业务", "priority": "高", "complexity": "中", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 2, "name": "收入管理", "category": "核心业务", "priority": "高", "complexity": "高", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 3, "name": "认领公告", "category": "收入管理", "priority": "中", "complexity": "中", "tested": false, "documented": true, "coverage_status": "仅文档"}, {"id": 4, "name": "认领审核", "category": "收入管理", "priority": "中", "complexity": "中", "tested": false, "documented": true, "coverage_status": "仅文档"}, {"id": 5, "name": "渠道收入", "category": "收入管理", "priority": "高", "complexity": "中", "tested": false, "documented": true, "coverage_status": "仅文档"}, {"id": 6, "name": "账单明细", "category": "收入管理", "priority": "高", "complexity": "中", "tested": false, "documented": true, "coverage_status": "仅文档"}, {"id": 7, "name": "未入账清单", "category": "收入管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 8, "name": "筹款产品", "category": "收入管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 9, "name": "慈善备案", "category": "合规管理", "priority": "高", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 10, "name": "订单查询", "category": "收入管理", "priority": "中", "complexity": "低", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 11, "name": "支出订单", "category": "核心业务", "priority": "高", "complexity": "高", "tested": false, "documented": true, "coverage_status": "仅文档"}, {"id": 12, "name": "统计报表", "category": "核心业务", "priority": "高", "complexity": "中", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 13, "name": "配置管理", "category": "核心业务", "priority": "高", "complexity": "中", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 14, "name": "资金池管理", "category": "资金管理", "priority": "高", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 15, "name": "资金池", "category": "资金管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 16, "name": "收入调整", "category": "收入管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 17, "name": "隐藏资金池", "category": "资金管理", "priority": "低", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 18, "name": "资金池清理", "category": "资金管理", "priority": "低", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 19, "name": "预算决算", "category": "财务管理", "priority": "高", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 20, "name": "收入", "category": "收入管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 21, "name": "支出", "category": "核心业务", "priority": "高", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 22, "name": "导入记录", "category": "数据管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 23, "name": "物资管理", "category": "资产管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 24, "name": "库存管理", "category": "资产管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 25, "name": "仓管单据", "category": "资产管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 26, "name": "物品管理", "category": "资产管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 27, "name": "仓库管理", "category": "资产管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 28, "name": "支出管理", "category": "核心业务", "priority": "高", "complexity": "高", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 29, "name": "备用金", "category": "资金管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 30, "name": "外部请款", "category": "支出管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 31, "name": "项目报销", "category": "支出管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 32, "name": "行政报销", "category": "支出管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 33, "name": "支出调整", "category": "支出管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 34, "name": "支出退款", "category": "支出管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 35, "name": "捐赠退款", "category": "收入管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 36, "name": "商家退款", "category": "收入管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 37, "name": "银企直连", "category": "财务管理", "priority": "高", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 38, "name": "票据管理", "category": "核心业务", "priority": "高", "complexity": "中", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 39, "name": "票据催办", "category": "票据管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 40, "name": "支付汇总", "category": "财务管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 41, "name": "票据备份", "category": "票据管理", "priority": "中", "complexity": "低", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 42, "name": "项目分摊", "category": "项目管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 43, "name": "报销票据管理", "category": "票据管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 44, "name": "业务管理", "category": "核心业务", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 45, "name": "资助管理", "category": "项目管理", "priority": "高", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 46, "name": "项目管理", "category": "核心业务", "priority": "高", "complexity": "高", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 47, "name": "开票管理", "category": "票据管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 48, "name": "票据看板", "category": "票据管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 49, "name": "票据开具", "category": "票据管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 50, "name": "票据查询", "category": "票据管理", "priority": "中", "complexity": "低", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 51, "name": "订单核销", "category": "财务管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 52, "name": "核销失败", "category": "财务管理", "priority": "低", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 53, "name": "备份下载", "category": "数据管理", "priority": "中", "complexity": "低", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 54, "name": "票据接口", "category": "系统集成", "priority": "中", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 55, "name": "合作方管理", "category": "核心业务", "priority": "高", "complexity": "中", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 56, "name": "合同管理", "category": "合作管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 57, "name": "捐方管理", "category": "合作管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 58, "name": "财务统计", "category": "核心业务", "priority": "高", "complexity": "中", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 59, "name": "月末关账", "category": "财务管理", "priority": "高", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 60, "name": "月收入报表", "category": "财务管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 61, "name": "月收入结转", "category": "财务管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 62, "name": "业务收支汇总", "category": "财务管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 63, "name": "业务收支明细", "category": "财务管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 64, "name": "用友管理", "category": "系统集成", "priority": "中", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 65, "name": "会计科目", "category": "财务管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 66, "name": "辅助核算", "category": "财务管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 67, "name": "凭证确认", "category": "财务管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 68, "name": "凭证管理", "category": "财务管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 69, "name": "可变配置", "category": "系统管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 70, "name": "动态表单", "category": "系统管理", "priority": "中", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 71, "name": "动态标签", "category": "系统管理", "priority": "低", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 72, "name": "合同模板", "category": "合作管理", "priority": "中", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 73, "name": "安全审计", "category": "系统管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 74, "name": "操作日志", "category": "系统管理", "priority": "中", "complexity": "低", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 75, "name": "错误日志", "category": "系统管理", "priority": "中", "complexity": "低", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 76, "name": "组织管理", "category": "核心业务", "priority": "高", "complexity": "中", "tested": true, "documented": true, "coverage_status": "完全覆盖"}, {"id": 77, "name": "组织架构", "category": "组织管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 78, "name": "角色配置", "category": "系统管理", "priority": "高", "complexity": "中", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 79, "name": "资金权限", "category": "权限管理", "priority": "高", "complexity": "高", "tested": false, "documented": false, "coverage_status": "未覆盖"}, {"id": 80, "name": "更新记录", "category": "系统管理", "priority": "低", "complexity": "低", "tested": false, "documented": false, "coverage_status": "未覆盖"}]}