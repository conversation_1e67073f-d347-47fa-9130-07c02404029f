"""
深度交互式测试脚本
对Boss3和Huoban3系统进行全面的交互式测试
包括选项卡、按钮、表单等所有可交互元素
"""

import asyncio
import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, List, Any

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import SYSTEMS_CONFIG, PATHS, generate_filename
from playwright.async_api import async_playwright, Page

class DeepInteractiveTest:
    def __init__(self, system_key: str):
        self.system_key = system_key
        self.system_config = SYSTEMS_CONFIG[system_key]
        self.test_results = []
        self.interaction_log = []
        self.screenshot_counter = 1
        
    async def run_deep_test(self):
        """运行深度交互测试"""
        print(f"开始深度交互测试: {self.system_config['name']}")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            page = await browser.new_page()
            
            try:
                # 登录系统
                if await self.login_system(page):
                    # 深度测试所有功能模块
                    await self.deep_test_all_modules(page)
                    
                    # 生成详细报告
                    await self.generate_detailed_report()
                
            except Exception as e:
                print(f"深度测试异常: {str(e)}")
            finally:
                await browser.close()
    
    async def login_system(self, page: Page) -> bool:
        """登录系统"""
        try:
            print(f"登录 {self.system_config['name']}...")
            
            await page.goto(self.system_config["url"])
            await page.wait_for_load_state('domcontentloaded')
            
            # 截图登录页面
            await self.take_annotated_screenshot(page, "登录页面", "login_page")
            
            # 填写登录信息
            await page.fill('input[name="username"]', self.system_config["credentials"]["username"])
            await page.fill('input[type="password"]', self.system_config["credentials"]["password"])
            
            # 截图填写后的登录页面
            await self.take_annotated_screenshot(page, "填写登录信息", "login_filled")
            
            await page.click('button:has-text("登 录")')
            await page.wait_for_load_state('domcontentloaded', timeout=10000)
            
            # 检查登录结果
            current_url = page.url
            if current_url != self.system_config["url"]:
                print("✅ 登录成功")
                await self.take_annotated_screenshot(page, "登录成功主页", "homepage")
                return True
            else:
                print("❌ 登录失败")
                return False
                
        except Exception as e:
            print(f"登录异常: {str(e)}")
            return False
    
    async def deep_test_all_modules(self, page: Page):
        """深度测试所有功能模块"""
        print("开始深度测试所有功能模块...")
        
        # 获取所有功能菜单
        menu_items = await self.discover_menu_items(page)
        
        for i, menu_item in enumerate(menu_items[:10], 1):  # 测试前10个功能
            print(f"\n=== 深度测试功能 {i}: {menu_item['text']} ===")
            await self.deep_test_single_module(page, menu_item, i)
            
            # 每测试3个功能休息一下
            if i % 3 == 0:
                await page.wait_for_timeout(2000)
    
    async def discover_menu_items(self, page: Page) -> List[Dict]:
        """发现菜单项"""
        menu_selectors = [
            '[role="menuitem"]',
            'nav a',
            '.menu a',
            '.sidebar a',
            'ul.menu li a'
        ]
        
        all_items = []
        for selector in menu_selectors:
            try:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text()
                    if text and text.strip() and len(text.strip()) > 1:
                        all_items.append({
                            "text": text.strip(),
                            "selector": selector,
                            "element": element
                        })
            except:
                continue
        
        # 去重
        unique_items = []
        seen_texts = set()
        for item in all_items:
            if item["text"] not in seen_texts:
                seen_texts.add(item["text"])
                unique_items.append(item)
        
        return unique_items
    
    async def deep_test_single_module(self, page: Page, menu_item: Dict, module_index: int):
        """深度测试单个功能模块"""
        module_name = menu_item["text"]
        
        try:
            # 点击功能菜单
            await page.click(f'text="{module_name}"')
            await page.wait_for_load_state('domcontentloaded', timeout=8000)
            
            # 截图功能页面
            await self.take_annotated_screenshot(page, f"{module_name}主页面", f"module_{module_index}_main")
            
            # 测试选项卡
            await self.test_tabs(page, module_name, module_index)
            
            # 测试按钮
            await self.test_buttons(page, module_name, module_index)
            
            # 测试表单
            await self.test_forms(page, module_name, module_index)
            
            # 记录测试结果
            self.test_results.append({
                "module": module_name,
                "index": module_index,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "completed"
            })
            
        except Exception as e:
            print(f"测试模块 {module_name} 异常: {str(e)}")
            self.test_results.append({
                "module": module_name,
                "index": module_index,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "error",
                "error": str(e)
            })
    
    async def test_tabs(self, page: Page, module_name: str, module_index: int):
        """测试选项卡"""
        print(f"  测试 {module_name} 的选项卡...")
        
        # 查找选项卡元素
        tab_selectors = [
            '.tab', '.tabs li', '.nav-tabs li', 
            '[role="tab"]', '.ant-tabs-tab',
            '.el-tabs__item', '.tab-item'
        ]
        
        tabs_found = []
        for selector in tab_selectors:
            try:
                tabs = await page.query_selector_all(selector)
                for tab in tabs:
                    text = await tab.inner_text()
                    if text and text.strip():
                        tabs_found.append({
                            "text": text.strip(),
                            "element": tab,
                            "selector": selector
                        })
            except:
                continue
        
        if tabs_found:
            print(f"    发现 {len(tabs_found)} 个选项卡")
            
            for i, tab in enumerate(tabs_found[:5], 1):  # 最多测试5个选项卡
                try:
                    print(f"    点击选项卡: {tab['text']}")
                    await tab["element"].click()
                    await page.wait_for_timeout(1000)
                    
                    # 截图选项卡内容
                    await self.take_annotated_screenshot(
                        page, 
                        f"{module_name}-选项卡-{tab['text']}", 
                        f"module_{module_index}_tab_{i}"
                    )
                    
                    # 记录交互日志
                    self.interaction_log.append({
                        "type": "tab_click",
                        "module": module_name,
                        "tab_text": tab["text"],
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    })
                    
                except Exception as e:
                    print(f"    点击选项卡 {tab['text']} 失败: {str(e)}")
        else:
            print(f"    未发现选项卡")
    
    async def test_buttons(self, page: Page, module_name: str, module_index: int):
        """测试按钮"""
        print(f"  测试 {module_name} 的按钮...")
        
        # 查找按钮元素
        button_selectors = [
            'button', 'input[type="button"]', 'input[type="submit"]',
            '.btn', '.button', '[role="button"]',
            'a.btn', '.ant-btn', '.el-button'
        ]
        
        buttons_found = []
        for selector in button_selectors:
            try:
                buttons = await page.query_selector_all(selector)
                for button in buttons:
                    text = await button.inner_text()
                    if text and text.strip():
                        buttons_found.append({
                            "text": text.strip(),
                            "element": button,
                            "selector": selector
                        })
            except:
                continue
        
        if buttons_found:
            print(f"    发现 {len(buttons_found)} 个按钮")
            
            # 按钮分类
            priority_buttons = []
            for button in buttons_found:
                text = button["text"].lower()
                if any(keyword in text for keyword in ['新增', '添加', '创建', '查询', '搜索', '导出']):
                    priority_buttons.append(button)
            
            # 优先测试重要按钮
            test_buttons = priority_buttons[:8] if priority_buttons else buttons_found[:8]
            
            for i, button in enumerate(test_buttons, 1):
                try:
                    print(f"    测试按钮: {button['text']}")
                    
                    # 截图按钮点击前
                    await self.take_annotated_screenshot(
                        page, 
                        f"{module_name}-按钮点击前-{button['text']}", 
                        f"module_{module_index}_button_{i}_before"
                    )
                    
                    # 点击按钮（对于危险操作只截图不点击）
                    if not any(keyword in button["text"].lower() for keyword in ['删除', '移除', '清空']):
                        await button["element"].click()
                        await page.wait_for_timeout(2000)
                        
                        # 截图按钮点击后
                        await self.take_annotated_screenshot(
                            page, 
                            f"{module_name}-按钮点击后-{button['text']}", 
                            f"module_{module_index}_button_{i}_after"
                        )
                    
                    # 记录交互日志
                    self.interaction_log.append({
                        "type": "button_click",
                        "module": module_name,
                        "button_text": button["text"],
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    })
                    
                except Exception as e:
                    print(f"    测试按钮 {button['text']} 失败: {str(e)}")
        else:
            print(f"    未发现按钮")
    
    async def test_forms(self, page: Page, module_name: str, module_index: int):
        """测试表单"""
        print(f"  测试 {module_name} 的表单...")
        
        # 查找表单元素
        forms = await page.query_selector_all('form')
        inputs = await page.query_selector_all('input[type="text"], input[type="email"], textarea')
        selects = await page.query_selector_all('select')
        
        if forms or inputs or selects:
            print(f"    发现表单元素: {len(forms)}个表单, {len(inputs)}个输入框, {len(selects)}个下拉框")
            
            # 截图表单
            await self.take_annotated_screenshot(
                page, 
                f"{module_name}-表单元素", 
                f"module_{module_index}_forms"
            )
            
            # 测试输入框（填写测试数据）
            for i, input_elem in enumerate(inputs[:3], 1):  # 最多测试3个输入框
                try:
                    placeholder = await input_elem.get_attribute('placeholder') or '测试数据'
                    await input_elem.fill(f"测试_{i}")
                    await page.wait_for_timeout(500)
                    
                    print(f"    填写输入框 {i}: {placeholder}")
                    
                except Exception as e:
                    print(f"    填写输入框 {i} 失败: {str(e)}")
            
            # 记录表单交互
            self.interaction_log.append({
                "type": "form_interaction",
                "module": module_name,
                "forms_count": len(forms),
                "inputs_count": len(inputs),
                "selects_count": len(selects),
                "timestamp": datetime.now().strftime("%H:%M:%S")
            })
        else:
            print(f"    未发现表单元素")
    
    async def take_annotated_screenshot(self, page: Page, description: str, filename_suffix: str):
        """截图并准备标注"""
        screenshot_path = os.path.join(
            PATHS["screenshots"], 
            f"{self.system_key}_{filename_suffix}_{self.screenshot_counter:03d}_{datetime.now().strftime('%H%M%S')}.png"
        )
        
        try:
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"    📸 截图: {description}")
            
            # 记录截图信息（后续用于标注）
            self.interaction_log.append({
                "type": "screenshot",
                "description": description,
                "path": screenshot_path,
                "counter": self.screenshot_counter,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            })
            
            self.screenshot_counter += 1
            
        except Exception as e:
            print(f"    截图失败: {str(e)}")
    
    async def generate_detailed_report(self):
        """生成详细测试报告"""
        print("生成深度测试报告...")
        
        report_content = f"""# {self.system_config['name']} 深度交互测试报告

## 测试概述

- **测试时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **测试系统**: {self.system_config['name']}
- **测试类型**: 深度交互式测试
- **测试模块数**: {len(self.test_results)}
- **截图数量**: {self.screenshot_counter - 1}

## 测试结果汇总

"""
        
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "completed" else "❌"
            report_content += f"### {status_icon} {result['module']}\n\n"
            report_content += f"- **测试时间**: {result['timestamp']}\n"
            report_content += f"- **测试状态**: {result['status']}\n"
            if result.get('error'):
                report_content += f"- **错误信息**: {result['error']}\n"
            report_content += "\n"
        
        # 添加交互日志统计
        tab_clicks = len([log for log in self.interaction_log if log["type"] == "tab_click"])
        button_clicks = len([log for log in self.interaction_log if log["type"] == "button_click"])
        form_interactions = len([log for log in self.interaction_log if log["type"] == "form_interaction"])
        screenshots = len([log for log in self.interaction_log if log["type"] == "screenshot"])
        
        report_content += f"""## 交互统计

- **选项卡点击**: {tab_clicks} 次
- **按钮点击**: {button_clicks} 次  
- **表单交互**: {form_interactions} 次
- **截图数量**: {screenshots} 张

## 详细交互日志

"""
        
        for log in self.interaction_log:
            if log["type"] != "screenshot":  # 截图日志太多，只显示交互日志
                report_content += f"- **{log['timestamp']}** [{log['type']}] "
                if log["type"] == "tab_click":
                    report_content += f"点击选项卡: {log['tab_text']} (模块: {log['module']})\n"
                elif log["type"] == "button_click":
                    report_content += f"点击按钮: {log['button_text']} (模块: {log['module']})\n"
                elif log["type"] == "form_interaction":
                    report_content += f"表单交互 (模块: {log['module']}, 表单数: {log['forms_count']}, 输入框数: {log['inputs_count']})\n"
        
        report_content += f"""

---
*深度测试报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_file = os.path.join(
            PATHS["reports"], 
            generate_filename(self.system_key, "深度交互测试报告", "md")
        )
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"深度测试报告已保存: {report_file}")
        
        # 保存交互日志JSON
        log_file = os.path.join(
            PATHS["data"], 
            generate_filename(self.system_key, "交互日志", "json")
        )
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.interaction_log, f, ensure_ascii=False, indent=2)
        
        print(f"交互日志已保存: {log_file}")

async def main():
    """主函数"""
    print("开始深度交互式测试...")
    
    # 测试Boss3系统
    print("\n" + "="*60)
    print("开始Boss3系统深度交互测试")
    print("="*60)
    
    boss3_tester = DeepInteractiveTest("boss3")
    await boss3_tester.run_deep_test()
    
    # 测试Huoban3系统
    print("\n" + "="*60)
    print("开始Huoban3系统深度交互测试")
    print("="*60)
    
    huoban3_tester = DeepInteractiveTest("huoban3")
    await huoban3_tester.run_deep_test()
    
    print("\n深度交互式测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
