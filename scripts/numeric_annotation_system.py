"""
纯数字标注系统
使用红色方框、箭头和阿拉伯数字进行标注，避免中文字符显示问题
"""

import os
import sys
import json
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import glob
import math

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import PATHS, generate_filename

class NumericAnnotationSystem:
    def __init__(self):
        self.screenshots_dir = PATHS["screenshots"]
        self.annotated_dir = os.path.join(PATHS["screenshots"], "annotated_numeric")
        self.annotation_log = []
        
        # 创建数字标注目录
        os.makedirs(self.annotated_dir, exist_ok=True)
        
        # 标注样式配置
        self.style = {
            "primary_color": (255, 0, 0),      # 红色
            "secondary_color": (255, 100, 100), # 浅红色
            "background_color": (255, 255, 255), # 白色背景
            "box_width": 4,                     # 方框线宽
            "arrow_width": 6,                   # 箭头线宽
            "number_size": 32,                  # 数字大小
            "circle_radius": 25,                # 数字圆圈半径
        }
        
        # 初始化字体（仅用于数字）
        self.number_font = self._load_number_font()
        
    def _load_number_font(self):
        """加载数字字体"""
        try:
            # 尝试加载系统字体
            font_paths = [
                "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",
                "/System/Library/Fonts/Helvetica.ttc",
                "C:/Windows/Fonts/arial.ttf"
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, self.style["number_size"])
                    print(f"✅ 加载数字字体: {os.path.basename(font_path)}")
                    return font
            
            # 使用默认字体
            font = ImageFont.load_default()
            print("⚠️ 使用默认字体")
            return font
            
        except Exception as e:
            print(f"❌ 字体加载失败: {str(e)}")
            return None
    
    def annotate_all_screenshots(self):
        """标注所有截图"""
        print("开始使用数字标注系统处理截图...")
        
        # 获取所有Boss3截图
        boss3_screenshots = glob.glob(os.path.join(self.screenshots_dir, "boss3_improved_*.png"))
        boss3_screenshots.sort()
        
        if not boss3_screenshots:
            print("❌ 未找到Boss3截图文件")
            return
        
        print(f"发现 {len(boss3_screenshots)} 张Boss3截图")
        
        # 按模块分组处理
        modules = self._group_screenshots(boss3_screenshots)
        
        total_processed = 0
        for module_key, screenshots in modules.items():
            print(f"\n📁 处理模块 {module_key}")
            processed = self._process_module(module_key, screenshots)
            total_processed += processed
        
        print(f"\n✅ 数字标注完成！总计处理 {total_processed} 张截图")
        
        # 生成标注说明文档
        self._generate_annotation_guide()
        
        return total_processed
    
    def _group_screenshots(self, screenshots):
        """按模块分组截图"""
        modules = {}
        
        for screenshot in screenshots:
            filename = os.path.basename(screenshot)
            
            if "login" in filename:
                module_key = "00_LOGIN"
            elif "homepage" in filename:
                module_key = "00_HOME"
            elif "module_" in filename:
                # 提取模块编号
                parts = filename.split("_")
                for i, part in enumerate(parts):
                    if part == "module" and i + 1 < len(parts):
                        module_key = f"{parts[i + 1]}_MODULE"
                        break
                else:
                    module_key = "99_OTHER"
            else:
                module_key = "99_OTHER"
            
            if module_key not in modules:
                modules[module_key] = []
            modules[module_key].append(screenshot)
        
        return modules
    
    def _process_module(self, module_key, screenshots):
        """处理单个模块的截图"""
        processed_count = 0
        
        for i, screenshot in enumerate(screenshots, 1):
            try:
                print(f"  📸 处理 {i}/{len(screenshots)}: {os.path.basename(screenshot)}")
                
                # 确定截图类型
                screenshot_type = self._determine_type(screenshot)
                
                # 进行数字标注
                success = self._annotate_screenshot(screenshot, module_key, i, screenshot_type)
                
                if success:
                    processed_count += 1
                    print(f"    ✅ 标注完成")
                else:
                    print(f"    ❌ 标注失败")
                    
            except Exception as e:
                print(f"    ❌ 处理失败: {str(e)}")
        
        return processed_count
    
    def _determine_type(self, screenshot_path):
        """确定截图类型"""
        filename = os.path.basename(screenshot_path).lower()
        
        if "login" in filename:
            return "LOGIN"
        elif "homepage" in filename:
            return "HOME"
        elif "btn" in filename and "before" in filename:
            return "BTN_BEFORE"
        elif "btn" in filename and "after" in filename:
            return "BTN_AFTER"
        elif "tab" in filename:
            return "TAB"
        elif "forms" in filename:
            return "FORM"
        elif "main" in filename:
            return "MAIN"
        else:
            return "GENERAL"
    
    def _annotate_screenshot(self, screenshot_path, module_key, sequence, screenshot_type):
        """标注单张截图"""
        try:
            # 加载图片
            image = Image.open(screenshot_path)
            draw = ImageDraw.Draw(image)
            width, height = image.size
            
            # 添加序号标注（左上角）
            self._add_sequence_number(draw, sequence, 50, 50)
            
            # 添加模块编号（右上角）
            module_number = module_key.split("_")[0]
            self._add_module_number(draw, module_number, width - 80, 50)
            
            # 根据类型添加特定标注
            self._add_type_annotations(draw, width, height, screenshot_type)
            
            # 保存标注后的图片
            output_filename = f"NUM_{module_key}_{sequence:02d}_{screenshot_type}_{os.path.basename(screenshot_path)}"
            output_path = os.path.join(self.annotated_dir, output_filename)
            image.save(output_path, quality=95)
            
            # 记录日志
            self.annotation_log.append({
                "original": screenshot_path,
                "annotated": output_path,
                "module": module_key,
                "sequence": sequence,
                "type": screenshot_type,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            
            return True
            
        except Exception as e:
            print(f"      错误: {str(e)}")
            return False
    
    def _add_sequence_number(self, draw, number, x, y):
        """添加序号（红色圆圈中的白色数字）"""
        try:
            # 绘制红色圆圈
            radius = self.style["circle_radius"]
            draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                        fill=self.style["primary_color"], 
                        outline=(128, 0, 0), width=3)
            
            # 绘制白色数字
            if self.number_font:
                text = str(number)
                bbox = draw.textbbox((0, 0), text, font=self.number_font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                draw.text((x - text_width//2, y - text_height//2), text, 
                         fill=self.style["background_color"], font=self.number_font)
            
        except Exception as e:
            print(f"      添加序号失败: {str(e)}")
    
    def _add_module_number(self, draw, module_number, x, y):
        """添加模块编号（红色方框中的白色数字）"""
        try:
            # 绘制红色方框
            size = self.style["circle_radius"] * 1.5
            draw.rectangle([x - size//2, y - size//2, x + size//2, y + size//2], 
                          fill=self.style["primary_color"], 
                          outline=(128, 0, 0), width=3)
            
            # 绘制白色数字
            if self.number_font:
                text = str(module_number)
                bbox = draw.textbbox((0, 0), text, font=self.number_font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                draw.text((x - text_width//2, y - text_height//2), text, 
                         fill=self.style["background_color"], font=self.number_font)
            
        except Exception as e:
            print(f"      添加模块编号失败: {str(e)}")
    
    def _add_type_annotations(self, draw, width, height, screenshot_type):
        """根据类型添加特定标注"""
        try:
            if screenshot_type == "LOGIN":
                self._annotate_login(draw, width, height)
            elif screenshot_type == "BTN_BEFORE":
                self._annotate_button_before(draw, width, height)
            elif screenshot_type == "BTN_AFTER":
                self._annotate_button_after(draw, width, height)
            elif screenshot_type == "TAB":
                self._annotate_tabs(draw, width, height)
            elif screenshot_type == "FORM":
                self._annotate_forms(draw, width, height)
            elif screenshot_type == "MAIN":
                self._annotate_main_page(draw, width, height)
            elif screenshot_type == "HOME":
                self._annotate_homepage(draw, width, height)
                
        except Exception as e:
            print(f"      添加类型标注失败: {str(e)}")
    
    def _annotate_login(self, draw, width, height):
        """标注登录页面"""
        # 用户名输入框
        self._draw_highlight_box(draw, width//2 - 180, height//2 - 100, 360, 40, 1)
        
        # 密码输入框
        self._draw_highlight_box(draw, width//2 - 180, height//2 - 40, 360, 40, 2)
        
        # 登录按钮
        self._draw_highlight_box(draw, width//2 - 90, height//2 + 20, 180, 50, 3)
        
        # 添加箭头指向登录按钮
        self._draw_arrow(draw, width//2 + 150, height//2 + 45, width//2 + 90, height//2 + 45)
    
    def _annotate_button_before(self, draw, width, height):
        """标注按钮点击前"""
        # 标注按钮区域
        self._draw_highlight_box(draw, 100, 200, 120, 40, 1)
        
        # 添加箭头指向按钮
        self._draw_arrow(draw, 50, 220, 100, 220)
    
    def _annotate_button_after(self, draw, width, height):
        """标注按钮点击后"""
        # 标注结果区域
        self._draw_highlight_box(draw, 100, 250, width - 200, 300, 1)
    
    def _annotate_tabs(self, draw, width, height):
        """标注选项卡"""
        # 标注选项卡区域
        self._draw_highlight_box(draw, 50, 200, width - 100, 50, 1)
        
        # 添加箭头
        self._draw_arrow(draw, width//2, 150, width//2, 200)
    
    def _annotate_forms(self, draw, width, height):
        """标注表单"""
        # 标注表单区域
        self._draw_highlight_box(draw, 100, 280, width - 200, height - 400, 1)
        
        # 标注几个输入框
        for i in range(3):
            y_pos = 320 + i * 60
            self._draw_highlight_box(draw, 150, y_pos, 300, 35, i + 2)
    
    def _annotate_main_page(self, draw, width, height):
        """标注主页面"""
        # 标注主要内容区域
        self._draw_highlight_box(draw, 50, 200, width - 100, height - 300, 1)
    
    def _annotate_homepage(self, draw, width, height):
        """标注首页"""
        # 标注导航区域
        self._draw_highlight_box(draw, 50, 150, width - 100, 100, 1)
        
        # 标注内容区域
        self._draw_highlight_box(draw, 50, 280, width - 100, height - 380, 2)
    
    def _draw_highlight_box(self, draw, x, y, width, height, number):
        """绘制高亮方框和编号"""
        # 绘制红色方框
        draw.rectangle([x, y, x + width, y + height], 
                      outline=self.style["primary_color"], 
                      width=self.style["box_width"])
        
        # 在方框左上角添加编号
        self._add_small_number(draw, number, x - 15, y - 15)
    
    def _add_small_number(self, draw, number, x, y):
        """添加小号数字标注"""
        try:
            # 绘制小圆圈
            radius = 15
            draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                        fill=self.style["primary_color"], 
                        outline=(128, 0, 0), width=2)
            
            # 绘制数字
            if self.number_font:
                # 使用较小的字体
                small_font = ImageFont.truetype(self.number_font.path, 20) if hasattr(self.number_font, 'path') else self.number_font
                text = str(number)
                bbox = draw.textbbox((0, 0), text, font=small_font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                draw.text((x - text_width//2, y - text_height//2), text, 
                         fill=self.style["background_color"], font=small_font)
            
        except Exception as e:
            print(f"      添加小号数字失败: {str(e)}")
    
    def _draw_arrow(self, draw, x1, y1, x2, y2):
        """绘制箭头"""
        try:
            # 绘制箭头线
            draw.line([x1, y1, x2, y2], fill=self.style["primary_color"], width=self.style["arrow_width"])
            
            # 计算箭头头部
            angle = math.atan2(y2 - y1, x2 - x1)
            arrow_length = 20
            arrow_angle = math.pi / 6
            
            # 箭头的两个分支
            x3 = x2 - arrow_length * math.cos(angle - arrow_angle)
            y3 = y2 - arrow_length * math.sin(angle - arrow_angle)
            x4 = x2 - arrow_length * math.cos(angle + arrow_angle)
            y4 = y2 - arrow_length * math.sin(angle + arrow_angle)
            
            # 绘制箭头头部
            draw.line([x2, y2, x3, y3], fill=self.style["primary_color"], width=self.style["arrow_width"])
            draw.line([x2, y2, x4, y4], fill=self.style["primary_color"], width=self.style["arrow_width"])
            
        except Exception as e:
            print(f"      绘制箭头失败: {str(e)}")
    
    def _generate_annotation_guide(self):
        """生成标注说明文档"""
        print("\n生成数字标注说明文档...")
        
        guide_content = f"""# Boss3系统数字标注说明文档

## 标注系统说明

本标注系统使用纯数字、红色方框和箭头进行标注，避免中文字符显示问题。

### 标注元素

1. **序号圆圈**: 左上角红色圆圈中的白色数字，表示截图在模块中的顺序
2. **模块编号**: 右上角红色方框中的白色数字，表示功能模块编号
3. **操作区域**: 红色方框标注的关键操作区域
4. **区域编号**: 操作区域左上角的小圆圈数字，表示操作顺序
5. **指向箭头**: 红色箭头指向重要的操作元素

### 模块编号对照表

- **00**: 登录和主页
- **01**: 数据看板
- **02**: 收入管理
- **03**: 支出管理
- **04**: 项目管理
- **05**: 财务统计
- **06**: 票据管理
- **07**: 合作方管理
- **08**: 组织管理
- **09**: 统计报表
- **10**: 配置管理

### 截图类型说明

#### LOGIN (登录页面)
- 区域1: 用户名输入框
- 区域2: 密码输入框
- 区域3: 登录按钮
- 箭头: 指向登录按钮

#### BTN_BEFORE (按钮点击前)
- 区域1: 待点击的按钮
- 箭头: 指向按钮

#### BTN_AFTER (按钮点击后)
- 区域1: 操作结果显示区域

#### TAB (选项卡)
- 区域1: 选项卡区域
- 箭头: 指向选项卡

#### FORM (表单)
- 区域1: 整个表单区域
- 区域2-4: 主要输入字段

#### MAIN (主页面)
- 区域1: 主要功能区域

#### HOME (系统首页)
- 区域1: 导航区域
- 区域2: 主要内容区域

### 使用说明

1. **查看截图**: 按模块编号和序号顺序查看
2. **理解标注**: 根据区域编号了解操作顺序
3. **跟随箭头**: 箭头指向需要特别关注的元素
4. **参考类型**: 根据文件名中的类型标识理解截图内容

### 文件命名规则

格式: `NUM_[模块]_[序号]_[类型]_[原始文件名]`

示例:
- `NUM_01_MODULE_01_MAIN_boss3_improved_module_01_main_004_165005.png`
- `NUM_00_LOGIN_01_LOGIN_boss3_improved_login_page_001_164959.png`

### 标注统计

- **处理截图总数**: {len(self.annotation_log)}
- **标注完成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **标注方式**: 纯数字 + 红色方框 + 箭头
- **字符编码**: 无中文字符，避免显示问题

---

**说明文档版本**: v1.0  
**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}  
**标注系统**: 数字标注系统 (无中文版本)
"""
        
        # 保存说明文档
        guide_file = os.path.join(
            PATHS["docs"], 
            generate_filename("数字标注", "说明文档", "md")
        )
        
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"数字标注说明文档已保存: {guide_file}")
        
        # 保存标注日志
        log_file = os.path.join(
            PATHS["data"], 
            generate_filename("数字标注", "处理日志", "json")
        )
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotation_log, f, ensure_ascii=False, indent=2)
        
        print(f"数字标注日志已保存: {log_file}")

def main():
    """主函数"""
    print("启动数字标注系统...")
    
    annotator = NumericAnnotationSystem()
    total_processed = annotator.annotate_all_screenshots()
    
    print(f"\n🎉 数字标注系统处理完成！")
    print(f"📊 总计处理: {total_processed} 张截图")
    print(f"📁 输出目录: {annotator.annotated_dir}")
    print(f"🔢 标注方式: 纯数字 + 红色方框 + 箭头")

if __name__ == "__main__":
    main()
