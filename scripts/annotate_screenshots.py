"""
截图标注处理脚本
为测试截图添加红色框线标注和序号，突出关键操作区域
"""

import os
import sys
import json
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import glob

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import PATHS, generate_filename

class ScreenshotAnnotator:
    def __init__(self):
        self.screenshots_dir = PATHS["screenshots"]
        self.annotated_dir = os.path.join(PATHS["screenshots"], "annotated")
        self.annotation_log = []
        
        # 创建标注目录
        os.makedirs(self.annotated_dir, exist_ok=True)
        
        # 标注样式配置
        self.annotation_style = {
            "box_color": (255, 0, 0),  # 红色
            "box_width": 3,
            "text_color": (255, 255, 255),  # 白色文字
            "text_bg_color": (255, 0, 0),  # 红色背景
            "font_size": 16,
            "sequence_font_size": 24
        }
    
    def annotate_boss3_screenshots(self):
        """标注Boss3系统的截图"""
        print("开始标注Boss3系统截图...")
        
        # 获取Boss3改进版测试的截图
        boss3_screenshots = glob.glob(os.path.join(self.screenshots_dir, "boss3_improved_*.png"))
        boss3_screenshots.sort()
        
        print(f"发现 {len(boss3_screenshots)} 张Boss3截图")
        
        # 按模块分组标注
        self.annotate_by_modules(boss3_screenshots)
        
        # 生成标注报告
        self.generate_annotation_report()
    
    def annotate_by_modules(self, screenshots):
        """按模块分组标注截图"""
        
        # 按模块分组
        modules = {}
        for screenshot in screenshots:
            filename = os.path.basename(screenshot)
            
            # 解析模块信息
            if "module_" in filename:
                parts = filename.split("_")
                module_index = None
                for i, part in enumerate(parts):
                    if part == "module" and i + 1 < len(parts):
                        module_index = parts[i + 1]
                        break
                
                if module_index:
                    if module_index not in modules:
                        modules[module_index] = []
                    modules[module_index].append(screenshot)
            else:
                # 登录页面等
                if "login" in filename:
                    if "login" not in modules:
                        modules["login"] = []
                    modules["login"].append(screenshot)
                elif "homepage" in filename:
                    if "homepage" not in modules:
                        modules["homepage"] = []
                    modules["homepage"].append(screenshot)
        
        # 为每个模块的截图添加标注
        for module_key, module_screenshots in modules.items():
            print(f"\n标注模块: {module_key}")
            self.annotate_module_screenshots(module_key, module_screenshots)
    
    def annotate_module_screenshots(self, module_key, screenshots):
        """标注单个模块的截图"""
        
        # 模块名称映射
        module_names = {
            "login": "登录流程",
            "homepage": "主页面",
            "01": "数据看板",
            "02": "收入管理", 
            "03": "支出管理",
            "04": "项目管理",
            "05": "财务统计",
            "06": "票据管理",
            "07": "合作方管理",
            "08": "组织管理",
            "09": "统计报表",
            "10": "配置管理"
        }
        
        module_name = module_names.get(module_key, f"模块{module_key}")
        
        for i, screenshot in enumerate(screenshots, 1):
            try:
                print(f"  标注截图 {i}/{len(screenshots)}: {os.path.basename(screenshot)}")
                
                # 加载图片
                image = Image.open(screenshot)
                draw = ImageDraw.Draw(image)
                
                # 添加标注
                self.add_annotations(draw, image, screenshot, module_name, i)
                
                # 保存标注后的图片
                annotated_filename = f"annotated_{module_key}_{i:02d}_{os.path.basename(screenshot)}"
                annotated_path = os.path.join(self.annotated_dir, annotated_filename)
                image.save(annotated_path, quality=95)
                
                # 记录标注日志
                self.annotation_log.append({
                    "original": screenshot,
                    "annotated": annotated_path,
                    "module": module_name,
                    "sequence": i,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
                
            except Exception as e:
                print(f"    ❌ 标注失败: {str(e)}")
    
    def add_annotations(self, draw, image, screenshot_path, module_name, sequence):
        """添加标注到图片"""
        
        width, height = image.size
        filename = os.path.basename(screenshot_path)
        
        # 添加序号标注（左上角）
        self.add_sequence_number(draw, sequence, 20, 20)
        
        # 添加模块名称（右上角）
        self.add_module_name(draw, module_name, width - 200, 20)
        
        # 根据截图类型添加特定标注
        if "login" in filename:
            self.add_login_annotations(draw, width, height)
        elif "btn" in filename and "before" in filename:
            self.add_button_annotations(draw, width, height, "点击前")
        elif "btn" in filename and "after" in filename:
            self.add_button_annotations(draw, width, height, "点击后")
        elif "tab" in filename:
            self.add_tab_annotations(draw, width, height)
        elif "forms" in filename:
            self.add_form_annotations(draw, width, height)
        elif "main" in filename:
            self.add_main_page_annotations(draw, width, height)
    
    def add_sequence_number(self, draw, sequence, x, y):
        """添加序号"""
        try:
            # 创建字体
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 
                                    self.annotation_style["sequence_font_size"])
        except:
            font = ImageFont.load_default()
        
        # 序号文字
        text = str(sequence)
        
        # 计算文字尺寸
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 绘制背景圆圈
        circle_radius = max(text_width, text_height) // 2 + 10
        draw.ellipse([x - circle_radius, y - circle_radius, 
                     x + circle_radius, y + circle_radius], 
                    fill=self.annotation_style["text_bg_color"])
        
        # 绘制序号文字
        draw.text((x - text_width//2, y - text_height//2), text, 
                 fill=self.annotation_style["text_color"], font=font)
    
    def add_module_name(self, draw, module_name, x, y):
        """添加模块名称"""
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 
                                    self.annotation_style["font_size"])
        except:
            font = ImageFont.load_default()
        
        # 计算文字尺寸
        bbox = draw.textbbox((0, 0), module_name, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 绘制背景矩形
        padding = 5
        draw.rectangle([x - padding, y - padding, 
                       x + text_width + padding, y + text_height + padding], 
                      fill=self.annotation_style["text_bg_color"])
        
        # 绘制文字
        draw.text((x, y), module_name, 
                 fill=self.annotation_style["text_color"], font=font)
    
    def add_login_annotations(self, draw, width, height):
        """添加登录页面标注"""
        # 标注用户名输入框区域（估算位置）
        self.add_highlight_box(draw, width//2 - 150, height//2 - 50, 300, 30, "用户名输入框")
        
        # 标注密码输入框区域
        self.add_highlight_box(draw, width//2 - 150, height//2, 300, 30, "密码输入框")
        
        # 标注登录按钮区域
        self.add_highlight_box(draw, width//2 - 75, height//2 + 60, 150, 40, "登录按钮")
    
    def add_button_annotations(self, draw, width, height, action_type):
        """添加按钮标注"""
        # 在页面顶部添加操作说明
        self.add_action_label(draw, width//2, 100, f"按钮{action_type}")
        
        # 标注可能的按钮区域（页面上方）
        self.add_highlight_box(draw, 50, 150, 100, 35, "操作按钮")
    
    def add_tab_annotations(self, draw, width, height):
        """添加选项卡标注"""
        # 标注选项卡区域
        self.add_highlight_box(draw, 50, 200, width - 100, 40, "选项卡区域")
        
        # 添加操作说明
        self.add_action_label(draw, width//2, 100, "选项卡切换")
    
    def add_form_annotations(self, draw, width, height):
        """添加表单标注"""
        # 标注表单区域
        self.add_highlight_box(draw, 100, 250, width - 200, height - 400, "表单区域")
        
        # 添加操作说明
        self.add_action_label(draw, width//2, 100, "表单填写")
    
    def add_main_page_annotations(self, draw, width, height):
        """添加主页面标注"""
        # 标注主要内容区域
        self.add_highlight_box(draw, 50, 150, width - 100, height - 200, "主要内容区域")
    
    def add_highlight_box(self, draw, x, y, width, height, label):
        """添加高亮框"""
        # 绘制红色边框
        draw.rectangle([x, y, x + width, y + height], 
                      outline=self.annotation_style["box_color"], 
                      width=self.annotation_style["box_width"])
        
        # 添加标签
        if label:
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 
                                        self.annotation_style["font_size"])
            except:
                font = ImageFont.load_default()
            
            # 标签背景
            bbox = draw.textbbox((0, 0), label, font=font)
            label_width = bbox[2] - bbox[0]
            label_height = bbox[3] - bbox[1]
            
            draw.rectangle([x, y - label_height - 5, x + label_width + 10, y], 
                          fill=self.annotation_style["text_bg_color"])
            
            # 标签文字
            draw.text((x + 5, y - label_height - 2), label, 
                     fill=self.annotation_style["text_color"], font=font)
    
    def add_action_label(self, draw, x, y, action):
        """添加操作说明标签"""
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 
                                    self.annotation_style["font_size"] + 4)
        except:
            font = ImageFont.load_default()
        
        # 计算文字尺寸
        bbox = draw.textbbox((0, 0), action, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 绘制背景
        padding = 10
        draw.rectangle([x - text_width//2 - padding, y - padding, 
                       x + text_width//2 + padding, y + text_height + padding], 
                      fill=self.annotation_style["text_bg_color"])
        
        # 绘制文字
        draw.text((x - text_width//2, y), action, 
                 fill=self.annotation_style["text_color"], font=font)
    
    def generate_annotation_report(self):
        """生成标注报告"""
        print("\n生成标注报告...")
        
        report_content = f"""# Boss3系统截图标注报告

## 标注概述

- **标注时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **原始截图数**: {len(self.annotation_log)}
- **标注截图数**: {len(self.annotation_log)}
- **标注目录**: {self.annotated_dir}

## 标注说明

### 标注元素
1. **序号标注**: 红色圆圈中的白色数字，表示操作顺序
2. **模块名称**: 右上角红色背景的模块名称
3. **操作区域**: 红色边框标注的关键操作区域
4. **操作说明**: 页面顶部的操作类型说明

### 标注类型
- **登录流程**: 标注用户名、密码输入框和登录按钮
- **按钮操作**: 标注按钮点击前后的状态变化
- **选项卡切换**: 标注选项卡区域和切换效果
- **表单填写**: 标注表单区域和输入字段
- **主页面**: 标注主要内容区域

## 标注文件列表

"""
        
        # 按模块分组显示
        modules = {}
        for log in self.annotation_log:
            module = log["module"]
            if module not in modules:
                modules[module] = []
            modules[module].append(log)
        
        for module, logs in modules.items():
            report_content += f"### {module}\n\n"
            for log in logs:
                filename = os.path.basename(log["annotated"])
                report_content += f"- **{log['sequence']:02d}**: {filename}\n"
            report_content += "\n"
        
        report_content += f"""## 使用建议

### 用户手册集成
1. 将标注截图按序号顺序插入用户手册
2. 每张截图配合相应的操作说明文字
3. 重点突出红色标注区域的操作要点

### 培训材料制作
1. 使用标注截图制作PPT培训材料
2. 按模块组织培训内容
3. 结合实际操作演示

### 问题排查
1. 用户遇到问题时可参考对应截图
2. 技术支持人员可快速定位问题区域
3. 便于远程协助和问题解决

---

**标注完成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}  
**标注工具**: PIL图像处理库  
**标注版本**: v1.0
"""
        
        # 保存报告
        report_file = os.path.join(
            PATHS["reports"], 
            generate_filename("boss3", "截图标注报告", "md")
        )
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"标注报告已保存: {report_file}")
        
        # 保存标注日志JSON
        log_file = os.path.join(
            PATHS["data"], 
            generate_filename("boss3", "标注日志", "json")
        )
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotation_log, f, ensure_ascii=False, indent=2)
        
        print(f"标注日志已保存: {log_file}")

def main():
    """主函数"""
    annotator = ScreenshotAnnotator()
    annotator.annotate_boss3_screenshots()

if __name__ == "__main__":
    main()
