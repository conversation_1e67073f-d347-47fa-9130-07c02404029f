"""
Huoban3系统详细测试脚本
"""

import asyncio
import os
import sys
import time
from datetime import datetime

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import SYSTEMS_CONFIG, PATHS, generate_filename
from playwright.async_api import async_playwright

async def test_huoban3_detailed():
    """详细测试Huoban3系统"""
    print("开始详细测试Huoban3系统...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        page = await browser.new_page()
        
        try:
            # 登录系统
            login_success = await login_huoban3(page)
            if not login_success:
                print("❌ 登录失败，无法继续测试")
                return
            
            # 详细分析登录后页面
            await analyze_huoban3_homepage(page)
            
            # 查找并测试功能模块
            await discover_huoban3_functions(page)
            
        except Exception as e:
            print(f"测试过程异常: {str(e)}")
        finally:
            await browser.close()

async def login_huoban3(page):
    """登录Huoban3系统"""
    try:
        system_config = SYSTEMS_CONFIG["huoban3"]
        print(f"访问Huoban3系统: {system_config['url']}")
        
        await page.goto(system_config["url"])
        await page.wait_for_load_state('domcontentloaded')
        
        # 截图登录页面
        screenshot_path = os.path.join(PATHS["screenshots"], 
                                     f"huoban3_login_detailed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        await page.screenshot(path=screenshot_path, full_page=True)
        print(f"登录页面截图: {screenshot_path}")
        
        # 填写登录信息
        await page.fill('input[name="username"]', system_config["credentials"]["username"])
        await page.fill('input[type="password"]', system_config["credentials"]["password"])
        await page.click('button:has-text("登 录")')
        
        # 等待登录完成
        await page.wait_for_load_state('domcontentloaded', timeout=10000)
        
        # 检查是否登录成功
        current_url = page.url
        if current_url != system_config["url"]:
            print("✅ Huoban3登录成功")
            
            # 截图登录后页面
            screenshot_path = os.path.join(PATHS["screenshots"], 
                                         f"huoban3_homepage_detailed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"主页截图: {screenshot_path}")
            
            return True
        else:
            print("❌ Huoban3登录失败")
            return False
            
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return False

async def analyze_huoban3_homepage(page):
    """分析Huoban3主页"""
    print("\n分析Huoban3主页结构...")
    
    try:
        # 获取页面标题
        title = await page.title()
        print(f"页面标题: {title}")
        
        # 获取页面URL
        url = page.url
        print(f"当前URL: {url}")
        
        # 查找所有链接
        links = await page.query_selector_all('a')
        print(f"发现 {len(links)} 个链接")
        
        link_texts = []
        for link in links[:20]:  # 只分析前20个链接
            try:
                text = await link.inner_text()
                href = await link.get_attribute('href')
                if text and text.strip():
                    link_texts.append({"text": text.strip(), "href": href})
            except:
                continue
        
        print("主要链接:")
        for i, link in enumerate(link_texts, 1):
            print(f"  {i}. {link['text']}")
            if link['href']:
                print(f"     -> {link['href']}")
        
        # 查找表单元素
        forms = await page.query_selector_all('form')
        inputs = await page.query_selector_all('input')
        buttons = await page.query_selector_all('button')
        
        print(f"\n页面元素统计:")
        print(f"  表单数量: {len(forms)}")
        print(f"  输入框数量: {len(inputs)}")
        print(f"  按钮数量: {len(buttons)}")
        
        # 查找导航菜单
        nav_selectors = [
            'nav', '.nav', '.menu', '.navigation', 
            '.sidebar', '.main-menu', '.top-menu',
            'ul.nav', 'ul.menu', '.navbar'
        ]
        
        for selector in nav_selectors:
            try:
                nav_elements = await page.query_selector_all(selector)
                if nav_elements:
                    print(f"发现导航元素: {selector} ({len(nav_elements)}个)")
            except:
                continue
        
        # 保存页面分析结果
        analysis_file = os.path.join(PATHS["data"], 
                                   f"huoban3_homepage_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        
        with open(analysis_file, 'w', encoding='utf-8') as f:
            f.write("Huoban3系统主页分析报告\n")
            f.write("="*40 + "\n\n")
            f.write(f"页面标题: {title}\n")
            f.write(f"页面URL: {url}\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("主要链接:\n")
            for i, link in enumerate(link_texts, 1):
                f.write(f"{i}. {link['text']}\n")
                if link['href']:
                    f.write(f"   链接: {link['href']}\n")
                f.write("\n")
            
            f.write(f"\n页面元素统计:\n")
            f.write(f"表单数量: {len(forms)}\n")
            f.write(f"输入框数量: {len(inputs)}\n")
            f.write(f"按钮数量: {len(buttons)}\n")
        
        print(f"主页分析报告已保存: {analysis_file}")
        
    except Exception as e:
        print(f"主页分析异常: {str(e)}")

async def discover_huoban3_functions(page):
    """发现Huoban3功能模块"""
    print("\n发现Huoban3功能模块...")
    
    try:
        # 尝试多种方式查找功能菜单
        menu_selectors = [
            'nav a', '.nav a', '.menu a', '.navigation a',
            '.sidebar a', '.main-menu a', '.top-menu a',
            'ul.nav li a', 'ul.menu li a', '.navbar a',
            '[role="menuitem"]', '.nav-item a', '.menu-item a',
            'a[href*="action"]', 'a[href*="page"]', 'a[href*="module"]'
        ]
        
        all_functions = []
        
        for selector in menu_selectors:
            try:
                elements = await page.query_selector_all(selector)
                print(f"选择器 '{selector}' 找到 {len(elements)} 个元素")
                
                for element in elements:
                    try:
                        text = await element.inner_text()
                        href = await element.get_attribute('href')
                        
                        if text and text.strip() and len(text.strip()) > 1:
                            # 过滤掉一些无关的链接
                            if not any(keyword in text.lower() for keyword in ['登出', 'logout', '首页', 'home']):
                                all_functions.append({
                                    "text": text.strip(),
                                    "href": href,
                                    "selector": selector
                                })
                    except:
                        continue
            except:
                continue
        
        # 去重
        unique_functions = []
        seen_texts = set()
        for func in all_functions:
            if func["text"] not in seen_texts:
                seen_texts.add(func["text"])
                unique_functions.append(func)
        
        print(f"发现 {len(unique_functions)} 个唯一功能模块:")
        for i, func in enumerate(unique_functions, 1):
            print(f"  {i}. {func['text']}")
        
        # 保存功能列表
        functions_file = os.path.join(PATHS["data"], 
                                    f"huoban3_functions_detailed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        
        with open(functions_file, 'w', encoding='utf-8') as f:
            f.write("Huoban3系统功能模块详细列表\n")
            f.write("="*35 + "\n\n")
            for i, func in enumerate(unique_functions, 1):
                f.write(f"{i}. {func['text']}\n")
                if func['href']:
                    f.write(f"   链接: {func['href']}\n")
                f.write(f"   选择器: {func['selector']}\n\n")
        
        print(f"功能列表已保存: {functions_file}")
        
        # 测试前几个功能
        if unique_functions:
            await test_huoban3_functions(page, unique_functions[:5])
        else:
            print("未发现可测试的功能模块")
            
            # 尝试查找页面中的所有可点击元素
            await find_clickable_elements(page)
        
    except Exception as e:
        print(f"功能发现异常: {str(e)}")

async def find_clickable_elements(page):
    """查找页面中所有可点击的元素"""
    print("\n查找所有可点击元素...")
    
    try:
        # 查找所有可能可点击的元素
        clickable_selectors = [
            'button', 'a', 'input[type="button"]', 'input[type="submit"]',
            '[onclick]', '[role="button"]', '.btn', '.button',
            'span[onclick]', 'div[onclick]', 'td[onclick]'
        ]
        
        all_clickable = []
        
        for selector in clickable_selectors:
            try:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    try:
                        text = await element.inner_text()
                        tag_name = await element.evaluate('el => el.tagName')
                        
                        if text and text.strip():
                            all_clickable.append({
                                "text": text.strip(),
                                "tag": tag_name,
                                "selector": selector
                            })
                    except:
                        continue
            except:
                continue
        
        # 去重并过滤
        unique_clickable = []
        seen_texts = set()
        for item in all_clickable:
            if item["text"] not in seen_texts and len(item["text"]) < 50:  # 过滤太长的文本
                seen_texts.add(item["text"])
                unique_clickable.append(item)
        
        print(f"发现 {len(unique_clickable)} 个可点击元素:")
        for i, item in enumerate(unique_clickable[:20], 1):  # 只显示前20个
            print(f"  {i}. {item['text']} ({item['tag']})")
        
        # 保存可点击元素列表
        clickable_file = os.path.join(PATHS["data"], 
                                    f"huoban3_clickable_elements_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        
        with open(clickable_file, 'w', encoding='utf-8') as f:
            f.write("Huoban3系统可点击元素列表\n")
            f.write("="*30 + "\n\n")
            for i, item in enumerate(unique_clickable, 1):
                f.write(f"{i}. {item['text']} ({item['tag']})\n")
                f.write(f"   选择器: {item['selector']}\n\n")
        
        print(f"可点击元素列表已保存: {clickable_file}")
        
    except Exception as e:
        print(f"查找可点击元素异常: {str(e)}")

async def test_huoban3_functions(page, functions):
    """测试Huoban3功能模块"""
    print(f"\n开始测试 {len(functions)} 个功能模块...")
    
    for i, func in enumerate(functions, 1):
        try:
            print(f"\n测试功能 {i}: {func['text']}")
            
            # 尝试点击功能
            if func['href'] and func['href'].startswith('http'):
                await page.goto(func['href'])
            else:
                await page.click(f'text="{func["text"]}"')
            
            # 等待页面加载
            await page.wait_for_load_state('domcontentloaded', timeout=5000)
            
            # 截图
            screenshot_path = os.path.join(PATHS["screenshots"], 
                                         f"huoban3_{func['text'].replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            await page.screenshot(path=screenshot_path)
            print(f"  截图: {os.path.basename(screenshot_path)}")
            
            # 获取页面信息
            title = await page.title()
            print(f"  页面标题: {title}")
            
            await page.wait_for_timeout(1000)
            
        except Exception as e:
            print(f"  ❌ 测试功能 '{func['text']}' 失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_huoban3_detailed())
