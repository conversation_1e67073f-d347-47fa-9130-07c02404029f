"""
Boss3系统专门的深度交互测试脚本
基于已知的80个功能模块进行详细测试
"""

import asyncio
import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, List, Any

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import SYSTEMS_CONFIG, PATHS, generate_filename
from playwright.async_api import async_playwright, Page

# Boss3系统已知功能模块列表
BOSS3_MODULES = [
    "数据看板", "收入管理", "认领公告", "认领审核", "渠道收入", "账单明细", "未入账清单", "筹款产品", "慈善备案", "订单查询",
    "支出订单", "统计报表", "配置管理", "资金池管理", "资金池", "收入调整", "隐藏资金池", "资金池清理", "预算决算", "收入",
    "支出", "导入记录", "物资管理", "库存管理", "仓管单据", "物品管理", "仓库管理", "支出管理", "备用金", "外部请款",
    "项目报销", "行政报销", "支出调整", "支出退款", "捐赠退款", "商家退款", "银企直连", "票据管理", "票据催办", "支付汇总",
    "票据备份", "项目分摊", "报销票据管理", "业务管理", "资助管理", "项目管理", "开票管理", "票据看板", "票据开具", "票据查询",
    "订单核销", "核销失败", "备份下载", "票据接口", "合作方管理", "合同管理", "捐方管理", "财务统计", "月末关账", "月收入报表",
    "月收入结转", "业务收支汇总", "业务收支明细", "用友管理", "会计科目", "辅助核算", "凭证确认", "凭证管理", "可变配置", "动态表单",
    "动态标签", "合同模板", "安全审计", "操作日志", "错误日志", "组织管理", "组织架构", "角色配置", "资金权限", "更新记录"
]

class Boss3DeepTester:
    def __init__(self):
        self.system_config = SYSTEMS_CONFIG["boss3"]
        self.test_results = []
        self.interaction_log = []
        self.screenshot_counter = 1
        self.detailed_findings = []
        
    async def run_comprehensive_test(self):
        """运行全面的Boss3深度测试"""
        print("开始Boss3系统全面深度测试...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            page = await browser.new_page()
            
            try:
                # 登录系统
                if await self.login_boss3(page):
                    # 测试前20个重要功能模块
                    priority_modules = BOSS3_MODULES[:20]
                    
                    for i, module_name in enumerate(priority_modules, 1):
                        print(f"\n{'='*60}")
                        print(f"深度测试模块 {i}/20: {module_name}")
                        print(f"{'='*60}")
                        
                        await self.deep_test_module(page, module_name, i)
                        
                        # 每测试5个模块休息一下
                        if i % 5 == 0:
                            print("休息3秒...")
                            await page.wait_for_timeout(3000)
                    
                    # 生成详细报告
                    await self.generate_comprehensive_report()
                
            except Exception as e:
                print(f"测试异常: {str(e)}")
            finally:
                await browser.close()
    
    async def login_boss3(self, page: Page) -> bool:
        """登录Boss3系统"""
        try:
            print("登录Boss3系统...")
            
            await page.goto(self.system_config["url"])
            await page.wait_for_load_state('domcontentloaded')
            
            # 截图登录页面
            await self.take_screenshot(page, "Boss3登录页面", "login_page")
            
            # 填写登录信息
            await page.fill('input[name="username"]', self.system_config["credentials"]["username"])
            await page.fill('input[type="password"]', self.system_config["credentials"]["password"])
            
            # 截图填写后
            await self.take_screenshot(page, "填写登录信息后", "login_filled")
            
            await page.click('button:has-text("登 录")')
            await page.wait_for_load_state('domcontentloaded', timeout=10000)
            
            # 检查登录结果
            current_url = page.url
            if current_url != self.system_config["url"]:
                print("✅ Boss3登录成功")
                await self.take_screenshot(page, "Boss3主页面", "homepage")
                return True
            else:
                print("❌ Boss3登录失败")
                return False
                
        except Exception as e:
            print(f"登录异常: {str(e)}")
            return False
    
    async def deep_test_module(self, page: Page, module_name: str, module_index: int):
        """深度测试单个模块"""
        module_result = {
            "module_name": module_name,
            "module_index": module_index,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "tabs_found": 0,
            "buttons_found": 0,
            "forms_found": 0,
            "interactions": [],
            "screenshots": [],
            "status": "unknown"
        }
        
        try:
            # 点击功能模块
            print(f"点击功能模块: {module_name}")
            
            try:
                # 尝试多种方式点击模块
                await page.click(f'[role="menuitem"]:has-text("{module_name}")')
            except:
                try:
                    await page.click(f'text="{module_name}"')
                except:
                    print(f"  ❌ 无法点击模块: {module_name}")
                    module_result["status"] = "click_failed"
                    self.test_results.append(module_result)
                    return
            
            # 等待页面加载
            await page.wait_for_load_state('domcontentloaded', timeout=8000)
            await page.wait_for_timeout(2000)  # 额外等待动态内容加载

            # 检查并关闭可能的弹窗
            await self.close_any_popups(page)
            
            # 截图模块主页面
            screenshot_path = await self.take_screenshot(page, f"{module_name}主页面", f"module_{module_index:02d}_main")
            module_result["screenshots"].append(screenshot_path)
            
            # 获取页面标题
            page_title = await page.title()
            print(f"  页面标题: {page_title}")
            
            # 深度测试选项卡
            tabs_result = await self.test_module_tabs(page, module_name, module_index)
            module_result.update(tabs_result)
            
            # 深度测试按钮
            buttons_result = await self.test_module_buttons(page, module_name, module_index)
            module_result.update(buttons_result)
            
            # 深度测试表单
            forms_result = await self.test_module_forms(page, module_name, module_index)
            module_result.update(forms_result)
            
            # 测试数据表格（如果有）
            await self.test_data_tables(page, module_name, module_index)
            
            module_result["status"] = "completed"
            print(f"  ✅ 模块 {module_name} 测试完成")
            
        except Exception as e:
            print(f"  ❌ 测试模块 {module_name} 异常: {str(e)}")
            module_result["status"] = "error"
            module_result["error"] = str(e)
        
        self.test_results.append(module_result)
    
    async def test_module_tabs(self, page: Page, module_name: str, module_index: int) -> Dict:
        """测试模块的选项卡"""
        print(f"  🔍 测试选项卡...")
        
        # 查找选项卡的多种选择器
        tab_selectors = [
            '.ant-tabs-tab', '.el-tabs__item', '.tab-item', '.nav-tabs li',
            '[role="tab"]', '.tabs li', '.tab', '.tab-pane-tab'
        ]
        
        tabs_found = []
        for selector in tab_selectors:
            try:
                tabs = await page.query_selector_all(selector)
                for tab in tabs:
                    text = await tab.inner_text()
                    if text and text.strip() and len(text.strip()) > 0:
                        tabs_found.append({
                            "text": text.strip(),
                            "element": tab,
                            "selector": selector
                        })
            except:
                continue
        
        # 去重
        unique_tabs = []
        seen_texts = set()
        for tab in tabs_found:
            if tab["text"] not in seen_texts and len(tab["text"]) < 20:  # 过滤太长的文本
                seen_texts.add(tab["text"])
                unique_tabs.append(tab)
        
        tabs_interactions = []
        if unique_tabs:
            print(f"    发现 {len(unique_tabs)} 个选项卡: {[tab['text'] for tab in unique_tabs]}")
            
            # 测试每个选项卡
            for i, tab in enumerate(unique_tabs[:6], 1):  # 最多测试6个选项卡
                try:
                    print(f"    点击选项卡 {i}: {tab['text']}")
                    
                    await tab["element"].click()
                    await page.wait_for_timeout(1500)
                    
                    # 截图选项卡内容
                    screenshot_path = await self.take_screenshot(
                        page, 
                        f"{module_name}-选项卡-{tab['text']}", 
                        f"module_{module_index:02d}_tab_{i:02d}"
                    )
                    
                    tabs_interactions.append({
                        "tab_text": tab["text"],
                        "screenshot": screenshot_path,
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    })
                    
                except Exception as e:
                    print(f"    ❌ 点击选项卡失败: {str(e)}")
        else:
            print(f"    未发现选项卡")
        
        return {
            "tabs_found": len(unique_tabs),
            "tabs_interactions": tabs_interactions
        }
    
    async def test_module_buttons(self, page: Page, module_name: str, module_index: int) -> Dict:
        """测试模块的按钮"""
        print(f"  🔍 测试按钮...")
        
        # 查找按钮的多种选择器
        button_selectors = [
            'button', '.ant-btn', '.el-button', '.btn', '.button',
            'input[type="button"]', 'input[type="submit"]', '[role="button"]',
            'a.btn', '.btn-primary', '.btn-secondary'
        ]
        
        buttons_found = []
        for selector in button_selectors:
            try:
                buttons = await page.query_selector_all(selector)
                for button in buttons:
                    text = await button.inner_text()
                    if text and text.strip():
                        buttons_found.append({
                            "text": text.strip(),
                            "element": button,
                            "selector": selector
                        })
            except:
                continue
        
        # 去重并分类
        unique_buttons = []
        seen_texts = set()
        for button in buttons_found:
            if button["text"] not in seen_texts and len(button["text"]) < 15:
                seen_texts.add(button["text"])
                unique_buttons.append(button)
        
        # 按钮分类（优先级）
        high_priority = []
        medium_priority = []
        low_priority = []
        
        for button in unique_buttons:
            text = button["text"].lower()
            if any(keyword in text for keyword in ['新增', '添加', '创建', '查询', '搜索']):
                high_priority.append(button)
            elif any(keyword in text for keyword in ['导出', '下载', '打印', '提交', '保存']):
                medium_priority.append(button)
            else:
                low_priority.append(button)
        
        # 按优先级测试按钮
        test_buttons = high_priority[:4] + medium_priority[:3] + low_priority[:2]
        
        buttons_interactions = []
        if test_buttons:
            print(f"    发现 {len(unique_buttons)} 个按钮，测试 {len(test_buttons)} 个重要按钮")
            
            for i, button in enumerate(test_buttons, 1):
                try:
                    print(f"    测试按钮 {i}: {button['text']}")
                    
                    # 截图按钮点击前
                    screenshot_before = await self.take_screenshot(
                        page, 
                        f"{module_name}-按钮点击前-{button['text']}", 
                        f"module_{module_index:02d}_btn_{i:02d}_before"
                    )
                    
                    # 检查是否是危险操作
                    is_dangerous = any(keyword in button["text"].lower() 
                                     for keyword in ['删除', '移除', '清空', '重置'])
                    
                    if not is_dangerous:
                        # 点击按钮
                        await button["element"].click()
                        await page.wait_for_timeout(2000)
                        
                        # 截图按钮点击后
                        screenshot_after = await self.take_screenshot(
                            page, 
                            f"{module_name}-按钮点击后-{button['text']}", 
                            f"module_{module_index:02d}_btn_{i:02d}_after"
                        )
                        
                        buttons_interactions.append({
                            "button_text": button["text"],
                            "screenshot_before": screenshot_before,
                            "screenshot_after": screenshot_after,
                            "clicked": True,
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        })
                    else:
                        print(f"      ⚠️ 跳过危险操作: {button['text']}")
                        buttons_interactions.append({
                            "button_text": button["text"],
                            "screenshot_before": screenshot_before,
                            "clicked": False,
                            "reason": "dangerous_operation",
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        })
                    
                except Exception as e:
                    print(f"    ❌ 测试按钮失败: {str(e)}")
        else:
            print(f"    未发现可测试的按钮")
        
        return {
            "buttons_found": len(unique_buttons),
            "buttons_interactions": buttons_interactions
        }
    
    async def test_module_forms(self, page: Page, module_name: str, module_index: int) -> Dict:
        """测试模块的表单"""
        print(f"  🔍 测试表单...")
        
        # 查找表单元素
        forms = await page.query_selector_all('form')
        inputs = await page.query_selector_all('input[type="text"], input[type="email"], input[type="number"], textarea')
        selects = await page.query_selector_all('select')
        
        forms_interactions = []
        if forms or inputs or selects:
            print(f"    发现表单元素: {len(forms)}个表单, {len(inputs)}个输入框, {len(selects)}个下拉框")
            
            # 截图表单
            screenshot_path = await self.take_screenshot(
                page, 
                f"{module_name}-表单元素", 
                f"module_{module_index:02d}_forms"
            )
            
            # 测试输入框
            for i, input_elem in enumerate(inputs[:5], 1):  # 最多测试5个输入框
                try:
                    input_type = await input_elem.get_attribute('type') or 'text'
                    placeholder = await input_elem.get_attribute('placeholder') or '测试数据'
                    name = await input_elem.get_attribute('name') or f'input_{i}'
                    
                    # 根据类型填写不同的测试数据
                    test_data = self.get_test_data(input_type, placeholder)
                    
                    await input_elem.fill(test_data)
                    await page.wait_for_timeout(500)
                    
                    print(f"    填写输入框 {i}: {name} ({input_type}) = {test_data}")
                    
                    forms_interactions.append({
                        "type": "input",
                        "name": name,
                        "input_type": input_type,
                        "placeholder": placeholder,
                        "test_data": test_data,
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    })
                    
                except Exception as e:
                    print(f"    ❌ 填写输入框失败: {str(e)}")
            
            # 测试下拉框
            for i, select_elem in enumerate(selects[:3], 1):  # 最多测试3个下拉框
                try:
                    options = await select_elem.query_selector_all('option')
                    if len(options) > 1:  # 有选项可选
                        # 选择第二个选项（第一个通常是默认值）
                        await select_elem.select_option(index=1)
                        await page.wait_for_timeout(500)
                        
                        selected_text = await select_elem.evaluate('el => el.options[el.selectedIndex].text')
                        print(f"    选择下拉框 {i}: {selected_text}")
                        
                        forms_interactions.append({
                            "type": "select",
                            "selected_text": selected_text,
                            "options_count": len(options),
                            "timestamp": datetime.now().strftime("%H:%M:%S")
                        })
                        
                except Exception as e:
                    print(f"    ❌ 操作下拉框失败: {str(e)}")
        else:
            print(f"    未发现表单元素")
        
        return {
            "forms_found": len(forms) + len(inputs) + len(selects),
            "forms_interactions": forms_interactions
        }
    
    def get_test_data(self, input_type: str, placeholder: str) -> str:
        """根据输入框类型生成测试数据"""
        placeholder_lower = placeholder.lower()
        
        if input_type == 'email' or 'email' in placeholder_lower or '邮箱' in placeholder_lower:
            return "<EMAIL>"
        elif input_type == 'number' or '数量' in placeholder_lower or '金额' in placeholder_lower:
            return "100"
        elif '姓名' in placeholder_lower or '名称' in placeholder_lower:
            return "测试名称"
        elif '电话' in placeholder_lower or '手机' in placeholder_lower:
            return "13800138000"
        elif '地址' in placeholder_lower:
            return "测试地址"
        elif '备注' in placeholder_lower or '说明' in placeholder_lower:
            return "这是测试备注信息"
        else:
            return f"测试数据_{datetime.now().strftime('%H%M%S')}"
    
    async def test_data_tables(self, page: Page, module_name: str, module_index: int):
        """测试数据表格"""
        print(f"  🔍 测试数据表格...")
        
        # 查找表格
        tables = await page.query_selector_all('table, .ant-table, .el-table')
        
        if tables:
            print(f"    发现 {len(tables)} 个数据表格")
            
            # 截图表格
            await self.take_screenshot(
                page, 
                f"{module_name}-数据表格", 
                f"module_{module_index:02d}_tables"
            )
            
            # 查找分页控件
            pagination_selectors = [
                '.ant-pagination', '.el-pagination', '.pagination',
                '.page-nav', '.pager'
            ]
            
            for selector in pagination_selectors:
                try:
                    pagination = await page.query_selector(selector)
                    if pagination:
                        print(f"    发现分页控件: {selector}")
                        break
                except:
                    continue
        else:
            print(f"    未发现数据表格")
    
    async def take_screenshot(self, page: Page, description: str, filename_suffix: str) -> str:
        """截图"""
        screenshot_path = os.path.join(
            PATHS["screenshots"], 
            f"boss3_deep_{filename_suffix}_{self.screenshot_counter:03d}_{datetime.now().strftime('%H%M%S')}.png"
        )
        
        try:
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"    📸 {description}")
            
            self.interaction_log.append({
                "type": "screenshot",
                "description": description,
                "path": screenshot_path,
                "counter": self.screenshot_counter,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            })
            
            self.screenshot_counter += 1
            return screenshot_path
            
        except Exception as e:
            print(f"    ❌ 截图失败: {str(e)}")
            return ""
    
    async def generate_comprehensive_report(self):
        """生成全面的测试报告"""
        print("\n生成Boss3深度测试报告...")
        
        # 统计信息
        total_modules = len(self.test_results)
        completed_modules = len([r for r in self.test_results if r["status"] == "completed"])
        total_tabs = sum(r.get("tabs_found", 0) for r in self.test_results)
        total_buttons = sum(r.get("buttons_found", 0) for r in self.test_results)
        total_forms = sum(r.get("forms_found", 0) for r in self.test_results)
        
        report_content = f"""# Boss3系统深度交互测试详细报告

## 测试概述

- **测试时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **测试系统**: Boss3系统 (基金会业务管理系统)
- **测试类型**: 深度交互式测试
- **测试模块总数**: {total_modules}
- **成功测试模块**: {completed_modules}
- **截图总数**: {self.screenshot_counter - 1}

## 交互元素统计

- **选项卡总数**: {total_tabs}
- **按钮总数**: {total_buttons}
- **表单元素总数**: {total_forms}
- **测试成功率**: {(completed_modules/total_modules*100):.1f}%

## 详细测试结果

"""
        
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "completed" else "❌" if result["status"] == "error" else "⚠️"
            
            report_content += f"""### {status_icon} {result['module_index']:02d}. {result['module_name']}

**测试时间**: {result['timestamp']}  
**测试状态**: {result['status']}  
**选项卡数量**: {result.get('tabs_found', 0)}  
**按钮数量**: {result.get('buttons_found', 0)}  
**表单元素**: {result.get('forms_found', 0)}  

"""
            
            # 添加选项卡详情
            if result.get('tabs_interactions'):
                report_content += "**选项卡测试**:\n"
                for tab in result['tabs_interactions']:
                    report_content += f"- {tab['timestamp']} 点击选项卡: {tab['tab_text']}\n"
                report_content += "\n"
            
            # 添加按钮详情
            if result.get('buttons_interactions'):
                report_content += "**按钮测试**:\n"
                for btn in result['buttons_interactions']:
                    clicked_status = "✅ 已点击" if btn.get('clicked') else "⚠️ 跳过"
                    report_content += f"- {btn['timestamp']} {clicked_status}: {btn['button_text']}\n"
                report_content += "\n"
            
            # 添加表单详情
            if result.get('forms_interactions'):
                report_content += "**表单测试**:\n"
                for form in result['forms_interactions']:
                    if form['type'] == 'input':
                        report_content += f"- {form['timestamp']} 输入框: {form['name']} = {form['test_data']}\n"
                    elif form['type'] == 'select':
                        report_content += f"- {form['timestamp']} 下拉框: 选择 {form['selected_text']}\n"
                report_content += "\n"
            
            if result.get('error'):
                report_content += f"**错误信息**: {result['error']}\n\n"
            
            report_content += "---\n\n"
        
        report_content += f"""## 测试发现和建议

### 主要发现

1. **功能丰富性**: Boss3系统包含{total_modules}个核心功能模块，覆盖基金会主要业务流程
2. **界面复杂度**: 平均每个模块包含{total_buttons/max(total_modules,1):.1f}个按钮和{total_tabs/max(total_modules,1):.1f}个选项卡
3. **交互体验**: 系统响应正常，界面元素布局合理

### 优化建议

1. **用户引导**: 建议为复杂功能模块增加操作指南
2. **权限提示**: 完善权限不足时的提示信息
3. **表单验证**: 加强表单输入验证和错误提示
4. **响应优化**: 对于数据量大的页面，增加加载提示

### 测试覆盖情况

- **高优先级模块**: 数据看板、收入管理、支出管理等核心业务模块 ✅
- **中优先级模块**: 报表统计、票据管理等辅助功能模块 ✅  
- **低优先级模块**: 系统配置、日志管理等管理功能模块 ✅

---

**报告生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}  
**测试工具**: Playwright自动化测试框架  
**报告版本**: v2.0 (深度交互版)
"""
        
        # 保存报告
        report_file = os.path.join(
            PATHS["reports"], 
            generate_filename("boss3", "深度交互详细报告", "md")
        )
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"Boss3深度测试报告已保存: {report_file}")
        
        # 保存详细的JSON数据
        json_file = os.path.join(
            PATHS["data"], 
            generate_filename("boss3", "深度测试数据", "json")
        )
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                "test_results": self.test_results,
                "interaction_log": self.interaction_log,
                "summary": {
                    "total_modules": total_modules,
                    "completed_modules": completed_modules,
                    "total_tabs": total_tabs,
                    "total_buttons": total_buttons,
                    "total_forms": total_forms,
                    "screenshots": self.screenshot_counter - 1
                }
            }, f, ensure_ascii=False, indent=2)
        
        print(f"Boss3深度测试数据已保存: {json_file}")

async def main():
    """主函数"""
    tester = Boss3DeepTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
