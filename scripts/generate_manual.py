"""
用户手册生成脚本
基于测试结果生成用户友好的操作手册
"""

import os
import sys
from datetime import datetime

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import SYSTEMS_CONFIG, PATHS, generate_filename

def generate_boss3_manual():
    """生成Boss3系统用户手册"""
    print("生成Boss3系统用户手册...")
    
    # Boss3功能模块（基于测试结果）
    boss3_functions = [
        {"name": "数据看板", "description": "查看系统整体数据统计和关键指标"},
        {"name": "收入管理", "description": "管理各类收入记录和资金流入"},
        {"name": "认领公告", "description": "发布和管理资金认领相关公告"},
        {"name": "认领审核", "description": "审核资金认领申请"},
        {"name": "渠道收入", "description": "管理不同渠道的收入来源"},
        {"name": "账单明细", "description": "查看详细的账单和交易记录"},
        {"name": "未入账清单", "description": "管理尚未入账的资金记录"},
        {"name": "筹款产品", "description": "管理筹款项目和产品"},
        {"name": "慈善备案", "description": "处理慈善项目的备案事务"},
        {"name": "订单查询", "description": "查询和管理各类订单信息"},
        {"name": "支出订单", "description": "管理资金支出订单"},
        {"name": "统计报表", "description": "生成各类统计分析报表"},
        {"name": "配置管理", "description": "系统配置和参数设置"},
        {"name": "资金池管理", "description": "管理不同用途的资金池"},
        {"name": "项目管理", "description": "管理公益项目的全生命周期"},
        {"name": "财务统计", "description": "财务数据统计和分析"},
        {"name": "票据管理", "description": "管理各类票据和凭证"},
        {"name": "合作方管理", "description": "管理合作伙伴信息"},
        {"name": "组织管理", "description": "管理组织架构和人员"},
        {"name": "系统设置", "description": "系统基础设置和配置"}
    ]
    
    manual_content = f"""# Boss3系统用户操作手册

## 系统概述

Boss3系统是上海联劝公益基金会的核心业务管理平台，主要用于管理基金会的收入、支出、项目、合作方等各项业务。

- **系统名称**: {SYSTEMS_CONFIG['boss3']['name']}
- **访问地址**: {SYSTEMS_CONFIG['boss3']['url']}
- **手册版本**: v1.0
- **更新日期**: {datetime.now().strftime('%Y年%m月%d日')}

## 快速开始

### 系统要求

- **推荐浏览器**: Chrome 80+、Firefox 75+、Safari 13+、Edge 80+
- **屏幕分辨率**: 建议不低于1024x768
- **网络要求**: 稳定的互联网连接

### 登录系统

1. **打开浏览器**，在地址栏输入系统地址：
   ```
   {SYSTEMS_CONFIG['boss3']['url']}
   ```

2. **输入登录凭据**：
   - 用户名：输入您的系统账号
   - 密码：输入您的登录密码

3. **点击"登录"按钮**完成登录

![Boss3登录页面](../screenshots/boss3_login_page_example.png)

> **注意事项**：
> - 请确保用户名和密码输入正确
> - 如果忘记密码，请联系系统管理员
> - 建议使用Chrome或Firefox浏览器以获得最佳体验

### 主界面介绍

登录成功后，您将看到Boss3系统的主界面，包含以下主要区域：

- **顶部导航栏**：显示系统名称和用户信息
- **左侧菜单栏**：包含所有功能模块的导航菜单
- **主要内容区**：显示当前选中功能的具体页面
- **底部信息栏**：显示系统版本和技术支持信息

![Boss3主界面](../screenshots/boss3_after_login_example.png)

## 功能模块详解

Boss3系统包含80个功能模块，以下是主要功能的详细说明：

"""
    
    # 添加功能模块说明
    for i, func in enumerate(boss3_functions, 1):
        manual_content += f"""### {i}. {func['name']}

**功能说明**: {func['description']}

**操作步骤**:
1. 在左侧菜单中找到并点击"{func['name']}"
2. 等待页面加载完成
3. 根据页面提示进行相应操作

**使用提示**:
- 请确保您有相应的操作权限
- 操作前请仔细阅读页面说明
- 如遇问题请及时联系技术支持

---

"""
    
    manual_content += """## 常见问题解答

### Q1: 登录时提示"用户名或密码错误"怎么办？
**A**: 请检查以下几点：
- 确认用户名和密码输入正确，注意大小写
- 检查是否有多余的空格
- 如果确认信息无误，请联系系统管理员重置密码

### Q2: 页面加载很慢或显示异常怎么办？
**A**: 建议尝试以下解决方案：
- 刷新页面（按F5键）
- 清除浏览器缓存和Cookie
- 检查网络连接是否稳定
- 尝试使用其他浏览器

### Q3: 如何安全退出系统？
**A**: 完成工作后，请点击页面右上角的"退出"或"登出"按钮安全退出系统。

### Q4: 忘记了某个功能在哪里怎么办？
**A**: 可以使用以下方法快速找到功能：
- 在左侧菜单中逐级查找
- 使用浏览器的查找功能（Ctrl+F）搜索功能名称
- 查阅本用户手册的功能索引

### Q5: 数据操作错误了怎么办？
**A**: 
- 如果系统支持撤销操作，请立即使用撤销功能
- 如果无法撤销，请立即联系系统管理员
- 重要操作前建议先备份相关数据

## 安全注意事项

1. **账号安全**：
   - 不要与他人共享登录账号
   - 定期更换密码
   - 离开时及时退出系统

2. **数据安全**：
   - 重要操作前请确认
   - 定期备份重要数据
   - 不要在公共场所操作敏感信息

3. **系统安全**：
   - 使用官方提供的访问地址
   - 不要安装来源不明的浏览器插件
   - 发现异常情况及时报告

## 技术支持

如果您在使用过程中遇到任何问题，请通过以下方式联系我们：

- **技术支持邮箱**: <EMAIL>
- **服务热线**: 400-XXX-XXXX
- **工作时间**: 周一至周五 9:00-18:00
- **在线支持**: 系统内置帮助功能

## 附录

### 浏览器兼容性

| 浏览器 | 最低版本 | 推荐版本 | 兼容性 |
|--------|----------|----------|--------|
| Chrome | 80 | 最新版本 | ✅ 完全支持 |
| Firefox | 75 | 最新版本 | ✅ 完全支持 |
| Safari | 13 | 最新版本 | ✅ 完全支持 |
| Edge | 80 | 最新版本 | ✅ 完全支持 |
| IE | - | - | ❌ 不支持 |

### 快捷键说明

| 快捷键 | 功能 |
|--------|------|
| F5 | 刷新页面 |
| Ctrl+F | 页面内查找 |
| Ctrl+S | 保存（如果支持） |
| Esc | 关闭弹窗 |

### 更新日志

- **v1.0** ({datetime.now().strftime('%Y-%m-%d')}): 初始版本发布，包含基础功能说明

---

*本手册基于Boss3系统自动化测试结果生成*  
*如有疑问或建议，请联系技术支持团队*
"""
    
    # 保存Markdown格式手册
    markdown_file = os.path.join(PATHS["docs"], generate_filename("boss3", "用户手册", "md"))
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(manual_content)
    
    print(f"Boss3用户手册(Markdown)已保存: {markdown_file}")
    
    # 生成HTML格式
    html_content = convert_markdown_to_html(manual_content, "Boss3系统用户操作手册")
    html_file = os.path.join(PATHS["docs"], generate_filename("boss3", "用户手册", "html"))
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"Boss3用户手册(HTML)已保存: {html_file}")
    
    return markdown_file, html_file

def generate_huoban3_manual():
    """生成Huoban3系统用户手册"""
    print("生成Huoban3系统用户手册...")

    manual_content = f"""# Huoban3系统用户操作手册

## 系统概述

Huoban3系统是上海联劝公益基金会的机构资助/拨款申请系统，主要用于合作伙伴机构申请资助和管理合作项目。

- **系统名称**: {SYSTEMS_CONFIG['huoban3']['name']}
- **访问地址**: {SYSTEMS_CONFIG['huoban3']['url']}
- **手册版本**: v1.0
- **更新日期**: {datetime.now().strftime('%Y年%m月%d日')}

## 快速开始

### 系统要求

- **推荐浏览器**: Chrome 80+、Firefox 75+、Safari 13+、Edge 80+
- **屏幕分辨率**: 建议不低于1024x768
- **网络要求**: 稳定的互联网连接

### 登录系统

1. **打开浏览器**，在地址栏输入系统地址：
   ```
   {SYSTEMS_CONFIG['huoban3']['url']}
   ```

2. **输入登录凭据**：
   - 用户名：输入您的机构账号
   - 密码：输入您的登录密码

3. **点击"登录"按钮**完成登录

![Huoban3登录页面](../screenshots/huoban3_login_page_example.png)

> **注意事项**：
> - 请使用机构注册时的账号信息
> - 如果是新机构，需要先进行合作申请
> - 如果忘记密码，请联系联劝基金会工作人员

### 新机构注册

如果您是新的合作机构，需要先进行注册：

1. **点击"合作申请"链接**
2. **填写机构基本信息**
3. **提交申请并等待审核**
4. **审核通过后获得登录账号**

## 主要功能

### 1. 资助申请

**功能说明**: 申请联劝基金会的资助项目

**操作步骤**:
1. 登录系统后进入申请页面
2. 选择合适的资助项目类型
3. 填写详细的申请信息
4. 上传必要的支持文档
5. 提交申请并等待审核

**申请材料准备**:
- 机构资质证明文件
- 项目计划书
- 预算明细表
- 其他相关证明材料

### 2. 项目管理

**功能说明**: 管理已获得资助的项目

**操作步骤**:
1. 查看项目列表和状态
2. 上传项目进度报告
3. 提交财务报告
4. 与基金会工作人员沟通

### 3. 文档管理

**功能说明**: 管理项目相关的各类文档

**操作步骤**:
1. 上传项目文档
2. 下载模板文件
3. 查看历史文档
4. 文档版本管理

## 常见问题解答

### Q1: 如何申请成为合作机构？
**A**:
1. 访问系统首页
2. 点击"合作申请"链接
3. 填写机构信息并提交
4. 等待联劝基金会审核
5. 审核通过后获得登录账号

### Q2: 忘记登录密码怎么办？
**A**: 请联系联劝基金会工作人员重置密码，联系方式：
- 邮箱：<EMAIL>
- 电话：021-XXXXXXXX

### Q3: 申请资助需要准备哪些材料？
**A**: 通常需要准备：
- 机构营业执照或注册证书
- 项目计划书
- 详细预算表
- 机构简介
- 项目负责人简历
- 其他相关证明材料

### Q4: 申请提交后多久能得到回复？
**A**: 一般情况下：
- 初步审核：5-10个工作日
- 详细评估：15-30个工作日
- 最终决定：根据项目复杂程度而定

### Q5: 如何查看申请进度？
**A**:
1. 登录系统
2. 进入"我的申请"页面
3. 查看申请状态和审核意见
4. 如有疑问可联系工作人员

## 操作注意事项

### 申请填写注意事项

1. **信息准确性**：
   - 确保所有信息真实准确
   - 联系方式保持畅通
   - 及时更新机构信息

2. **文档要求**：
   - 文件格式：PDF、Word、Excel
   - 文件大小：单个文件不超过10MB
   - 文件命名：使用有意义的文件名

3. **项目描述**：
   - 目标明确具体
   - 预算合理详细
   - 时间安排可行

### 系统使用注意事项

1. **定期登录**：
   - 建议每周至少登录一次
   - 及时查看系统通知
   - 关注申请状态变化

2. **数据备份**：
   - 重要文档请保留备份
   - 定期下载项目资料
   - 保存重要通信记录

## 技术支持

如果您在使用过程中遇到技术问题，请联系：

- **技术支持邮箱**: <EMAIL>
- **合作咨询邮箱**: <EMAIL>
- **服务热线**: 021-XXXXXXXX
- **工作时间**: 周一至周五 9:00-17:30

## 附录

### 文件格式要求

| 文件类型 | 支持格式 | 大小限制 | 说明 |
|----------|----------|----------|------|
| 文档 | PDF, DOC, DOCX | 10MB | 项目计划、报告等 |
| 表格 | XLS, XLSX | 5MB | 预算表、统计表等 |
| 图片 | JPG, PNG | 2MB | 证书、照片等 |

### 联系方式

- **上海联劝公益基金会**
- **地址**: 上海市XXXXXX
- **邮编**: 200000
- **网站**: www.lianquan.org.cn

---

*本手册基于Huoban3系统测试结果生成*
*如有疑问或建议，请联系我们*
"""

    # 保存Markdown格式手册
    markdown_file = os.path.join(PATHS["docs"], generate_filename("huoban3", "用户手册", "md"))
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(manual_content)

    print(f"Huoban3用户手册(Markdown)已保存: {markdown_file}")

    # 生成HTML格式
    html_content = convert_markdown_to_html(manual_content, "Huoban3系统用户操作手册")
    html_file = os.path.join(PATHS["docs"], generate_filename("huoban3", "用户手册", "html"))
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"Huoban3用户手册(HTML)已保存: {html_file}")

    return markdown_file, html_file

def convert_markdown_to_html(markdown_content, title):
    """将Markdown转换为HTML"""

    # 简单的Markdown到HTML转换
    html_content = markdown_content

    # 转换标题
    html_content = html_content.replace('# ', '<h1>').replace('\n## ', '</h1>\n<h2>').replace('\n### ', '</h2>\n<h3>')
    html_content = html_content.replace('\n#### ', '</h3>\n<h4>').replace('\n##### ', '</h4>\n<h5>')

    # 转换粗体
    import re
    html_content = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html_content)

    # 转换代码块
    html_content = re.sub(r'```(.*?)```', r'<pre><code>\1</code></pre>', html_content, flags=re.DOTALL)
    html_content = re.sub(r'`(.*?)`', r'<code>\1</code>', html_content)

    # 转换链接
    html_content = re.sub(r'\[(.*?)\]\((.*?)\)', r'<a href="\2">\1</a>', html_content)

    # 转换图片
    html_content = re.sub(r'!\[(.*?)\]\((.*?)\)', r'<img src="\2" alt="\1" title="\1">', html_content)

    # 转换表格（简单处理）
    lines = html_content.split('\n')
    in_table = False
    processed_lines = []

    for line in lines:
        if '|' in line and not line.strip().startswith('<'):
            if not in_table:
                processed_lines.append('<table>')
                in_table = True

            cells = [cell.strip() for cell in line.split('|')[1:-1]]  # 去掉首尾空元素
            if all('---' in cell or '-' in cell for cell in cells):  # 表格分隔行
                continue

            row_html = '<tr>'
            for cell in cells:
                if cell.startswith('**') and cell.endswith('**'):  # 表头
                    row_html += f'<th>{cell[2:-2]}</th>'
                else:
                    row_html += f'<td>{cell}</td>'
            row_html += '</tr>'
            processed_lines.append(row_html)
        else:
            if in_table:
                processed_lines.append('</table>')
                in_table = False
            processed_lines.append(line)

    if in_table:
        processed_lines.append('</table>')

    html_content = '\n'.join(processed_lines)

    # 转换段落
    paragraphs = html_content.split('\n\n')
    html_paragraphs = []
    for p in paragraphs:
        p = p.strip()
        if p and not p.startswith('<') and not p.startswith('|'):
            html_paragraphs.append(f'<p>{p}</p>')
        else:
            html_paragraphs.append(p)

    html_content = '\n\n'.join(html_paragraphs)

    # 完整的HTML模板
    full_html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }}
        .container {{
            background-color: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }}
        h2 {{
            color: #34495e;
            margin-top: 35px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }}
        h3 {{
            color: #2c3e50;
            margin-top: 25px;
            margin-bottom: 15px;
        }}
        h4, h5 {{
            color: #555;
            margin-top: 20px;
            margin-bottom: 10px;
        }}
        p {{
            margin-bottom: 15px;
            text-align: justify;
        }}
        img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 15px 0;
            display: block;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            background-color: white;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
            color: #2c3e50;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        code {{
            background-color: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }}
        pre {{
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
        }}
        pre code {{
            background-color: transparent;
            padding: 0;
        }}
        blockquote {{
            background-color: #f8f9fa;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }}
        a {{
            color: #3498db;
            text-decoration: none;
        }}
        a:hover {{
            text-decoration: underline;
        }}
        .toc {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }}
        .highlight {{
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }}
        .footer {{
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        {html_content}
        <div class="footer">
            <p>文档生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p>上海联劝公益基金会 | 技术支持</p>
        </div>
    </div>
</body>
</html>"""

    return full_html

def main():
    """主函数"""
    print("开始生成用户手册...")

    # 生成Boss3用户手册
    boss3_md, boss3_html = generate_boss3_manual()

    # 生成Huoban3用户手册
    huoban3_md, huoban3_html = generate_huoban3_manual()

    print("\n用户手册生成完成!")
    print("生成的文件:")
    print(f"- {boss3_md}")
    print(f"- {boss3_html}")
    print(f"- {huoban3_md}")
    print(f"- {huoban3_html}")

if __name__ == "__main__":
    main()
