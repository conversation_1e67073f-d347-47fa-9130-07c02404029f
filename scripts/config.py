"""
系统配置文件
包含两个测试系统的基本信息和测试配置
"""

import os
from datetime import datetime

# 系统配置信息
SYSTEMS_CONFIG = {
    "boss3": {
        "name": "Boss3系统",
        "url": "http://csboss.lianquan.org.cn",
        "credentials": {
            "username": "wa<PERSON><PERSON><PERSON><PERSON>",
            "password": "Ok756756@"
        },
        "description": "上海联劝公益基金会Boss3业务管理系统"
    },
    "huoban3": {
        "name": "Huoban3系统", 
        "url": "http://cspartner.lianquan.org.cn",
        "credentials": {
            "username": "测试企业0714",
            "password": "Lr775032@"
        },
        "description": "上海联劝公益基金会Huoban3合作伙伴系统"
    }
}

# 测试配置
TEST_CONFIG = {
    "browsers": ["chromium", "firefox", "webkit"],
    "headless": False,  # 设置为False以便观察测试过程
    "timeout": 30000,   # 30秒超时
    "screenshot_quality": 90,
    "screenshot_full_page": True,
    "wait_for_load_state": "networkidle"
}

# 文件路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PATHS = {
    "screenshots": os.path.join(BASE_DIR, "screenshots"),
    "docs": os.path.join(BASE_DIR, "docs"),
    "data": os.path.join(BASE_DIR, "data"),
    "reports": os.path.join(BASE_DIR, "reports"),
    "scripts": os.path.join(BASE_DIR, "scripts")
}

# 确保目录存在
for path in PATHS.values():
    os.makedirs(path, exist_ok=True)

# Bug严重程度分级
BUG_SEVERITY = {
    "P0": "致命 - 系统崩溃或核心功能完全无法使用",
    "P1": "严重 - 主要功能受影响，有替代方案",
    "P2": "一般 - 功能部分受影响，用户体验下降",
    "P3": "轻微 - 界面问题或非关键功能异常"
}

# Bug优先级
BUG_PRIORITY = {
    "高": "需要立即修复",
    "中": "下个版本修复",
    "低": "有时间时修复"
}

# 测试结果状态
TEST_STATUS = {
    "PASS": "通过",
    "FAIL": "失败", 
    "SKIP": "跳过",
    "ERROR": "错误"
}

# 生成时间戳
def get_timestamp():
    return datetime.now().strftime("%Y%m%d_%H%M%S")

# 生成文件名
def generate_filename(system_name, file_type, extension=""):
    timestamp = get_timestamp()
    if extension and not extension.startswith('.'):
        extension = '.' + extension
    return f"{system_name}_{file_type}_{timestamp}{extension}"

# 截图文件命名
def generate_screenshot_name(system_name, module_name, step="", sequence=1):
    timestamp = get_timestamp()
    if step:
        return f"{system_name}_{module_name}_{step}_{sequence:03d}_{timestamp}.png"
    else:
        return f"{system_name}_{module_name}_{sequence:03d}_{timestamp}.png"
