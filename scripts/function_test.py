"""
功能模块测试脚本
对发现的功能模块进行详细测试
"""

import asyncio
import os
import sys
import time
from datetime import datetime

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import SYSTEMS_CONFIG, PATHS, generate_filename
from playwright.async_api import async_playwright

# Boss3系统功能模块列表
BOSS3_FUNCTIONS = [
    "数据看板", "收入管理", "认领公告", "认领审核", "渠道收入", "账单明细", "未入账清单", "筹款产品", "慈善备案", "订单查询",
    "支出订单", "统计报表", "配置管理", "资金池管理", "资金池", "收入调整", "隐藏资金池", "资金池清理", "预算决算", "收入",
    "支出", "导入记录", "物资管理", "库存管理", "仓管单据", "物品管理", "仓库管理", "支出管理", "备用金", "外部请款",
    "项目报销", "行政报销", "支出调整", "支出退款", "捐赠退款", "商家退款", "银企直连", "票据管理", "票据催办", "支付汇总",
    "票据备份", "项目分摊", "报销票据管理", "业务管理", "资助管理", "项目管理", "开票管理", "票据看板", "票据开具", "票据查询",
    "订单核销", "核销失败", "备份下载", "票据接口", "合作方管理", "合同管理", "捐方管理", "财务统计", "月末关账", "月收入报表",
    "月收入结转", "业务收支汇总", "业务收支明细", "用友管理", "会计科目", "辅助核算", "凭证确认", "凭证管理", "可变配置", "动态表单",
    "动态标签", "合同模板", "安全审计", "操作日志", "错误日志", "组织管理", "组织架构", "角色配置", "资金权限", "更新记录"
]

async def test_boss3_functions():
    """测试Boss3系统功能模块"""
    print("开始测试Boss3系统功能模块...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        page = await browser.new_page()
        
        try:
            # 登录系统
            login_success = await login_boss3(page)
            if not login_success:
                print("❌ 登录失败，无法继续测试")
                return
            
            # 测试功能模块
            test_results = []
            for i, function_name in enumerate(BOSS3_FUNCTIONS[:20], 1):  # 测试前20个功能
                print(f"\n测试功能 {i}/20: {function_name}")
                result = await test_single_function(page, function_name, "boss3")
                test_results.append(result)
                
                # 每测试5个功能休息一下
                if i % 5 == 0:
                    print("休息2秒...")
                    await page.wait_for_timeout(2000)
            
            # 生成测试报告
            await generate_function_test_report(test_results, "boss3")
            
        except Exception as e:
            print(f"测试过程异常: {str(e)}")
        finally:
            await browser.close()

async def login_boss3(page):
    """登录Boss3系统"""
    try:
        system_config = SYSTEMS_CONFIG["boss3"]
        await page.goto(system_config["url"])
        await page.wait_for_load_state('domcontentloaded')
        
        # 填写登录信息
        await page.fill('input[name="username"]', system_config["credentials"]["username"])
        await page.fill('input[type="password"]', system_config["credentials"]["password"])
        await page.click('button:has-text("登 录")')
        
        # 等待登录完成
        await page.wait_for_load_state('domcontentloaded', timeout=10000)
        
        # 检查是否登录成功
        current_url = page.url
        if current_url != system_config["url"]:
            print("✅ Boss3登录成功")
            return True
        else:
            print("❌ Boss3登录失败")
            return False
            
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return False

async def test_single_function(page, function_name, system_key):
    """测试单个功能模块"""
    result = {
        "function_name": function_name,
        "system": system_key,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "status": "FAIL",
        "response_time": 0,
        "screenshot": "",
        "error_message": "",
        "page_title": "",
        "has_content": False
    }
    
    try:
        start_time = time.time()
        
        # 点击功能菜单
        try:
            await page.click(f'[role="menuitem"]:has-text("{function_name}")')
        except:
            # 如果直接点击失败，尝试其他方法
            await page.click(f'text="{function_name}"')
        
        # 等待页面加载
        await page.wait_for_load_state('domcontentloaded', timeout=8000)
        
        # 计算响应时间
        response_time = time.time() - start_time
        result["response_time"] = round(response_time, 2)
        
        # 获取页面标题
        try:
            page_title = await page.title()
            result["page_title"] = page_title
        except:
            result["page_title"] = "无法获取标题"
        
        # 截图
        screenshot_path = os.path.join(
            PATHS["screenshots"], 
            f"{system_key}_{function_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        )
        
        try:
            await page.screenshot(path=screenshot_path)
            result["screenshot"] = screenshot_path
            print(f"  截图: {os.path.basename(screenshot_path)}")
        except Exception as e:
            print(f"  截图失败: {str(e)}")
        
        # 检查页面内容
        try:
            # 检查是否有错误信息
            error_indicators = await page.query_selector_all('text=/错误|异常|失败|Error|Exception/')
            if error_indicators:
                result["error_message"] = "页面包含错误信息"
                result["status"] = "ERROR"
            else:
                # 检查页面是否有实际内容
                body_text = await page.inner_text('body')
                if len(body_text.strip()) > 100:  # 页面有足够的内容
                    result["has_content"] = True
                    result["status"] = "PASS"
                    print(f"  ✅ 功能正常，响应时间: {response_time:.2f}秒")
                else:
                    result["error_message"] = "页面内容过少"
                    result["status"] = "FAIL"
                    print(f"  ❌ 页面内容不足")
        except Exception as e:
            result["error_message"] = f"内容检查异常: {str(e)}"
            result["status"] = "ERROR"
            print(f"  ❌ 内容检查失败: {str(e)}")
        
    except Exception as e:
        result["error_message"] = str(e)
        result["status"] = "ERROR"
        print(f"  ❌ 测试失败: {str(e)}")
    
    return result

async def generate_function_test_report(test_results, system_key):
    """生成功能测试报告"""
    print(f"\n生成{system_key}功能测试报告...")
    
    # 统计信息
    total_tests = len(test_results)
    passed_tests = len([r for r in test_results if r["status"] == "PASS"])
    failed_tests = len([r for r in test_results if r["status"] == "FAIL"])
    error_tests = len([r for r in test_results if r["status"] == "ERROR"])
    
    # 生成Markdown报告
    report_content = f"""# {SYSTEMS_CONFIG[system_key]['name']} 功能测试报告

## 测试概述

- **测试时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **测试系统**: {SYSTEMS_CONFIG[system_key]['name']}
- **测试功能数**: {total_tests}
- **通过数量**: {passed_tests}
- **失败数量**: {failed_tests}
- **错误数量**: {error_tests}
- **通过率**: {(passed_tests/total_tests*100):.1f}%

## 详细测试结果

"""
    
    for result in test_results:
        status_icon = "✅" if result["status"] == "PASS" else "❌" if result["status"] == "FAIL" else "⚠️"
        
        report_content += f"""### {status_icon} {result['function_name']}

- **状态**: {result['status']}
- **响应时间**: {result['response_time']}秒
- **页面标题**: {result['page_title']}
- **有内容**: {'是' if result['has_content'] else '否'}
"""
        
        if result['error_message']:
            report_content += f"- **错误信息**: {result['error_message']}\n"
        
        if result['screenshot']:
            report_content += f"- **截图**: {os.path.basename(result['screenshot'])}\n"
        
        report_content += "\n"
    
    # 添加统计图表
    report_content += f"""## 测试统计

| 状态 | 数量 | 百分比 |
|------|------|--------|
| 通过 | {passed_tests} | {(passed_tests/total_tests*100):.1f}% |
| 失败 | {failed_tests} | {(failed_tests/total_tests*100):.1f}% |
| 错误 | {error_tests} | {(error_tests/total_tests*100):.1f}% |

## 建议

1. **优先修复错误状态的功能模块**
2. **检查失败功能的权限设置**
3. **优化响应时间超过3秒的功能**
4. **完善错误提示和用户引导**

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    report_file = os.path.join(
        PATHS["reports"], 
        generate_filename(system_key, "功能测试报告", "md")
    )
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"功能测试报告已保存: {report_file}")
    
    # 保存Excel格式的详细数据
    try:
        import pandas as pd
        df = pd.DataFrame(test_results)
        excel_file = os.path.join(
            PATHS["reports"], 
            generate_filename(system_key, "功能测试数据", "xlsx")
        )
        df.to_excel(excel_file, index=False, sheet_name="功能测试结果")
        print(f"测试数据已保存: {excel_file}")
    except Exception as e:
        print(f"保存Excel文件失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_boss3_functions())
