"""
系统测试主类
负责执行自动化测试和生成测试报告
"""

import asyncio
import json
import os
import time
from datetime import datetime
from typing import Dict, List, Any
from playwright.async_api import async_playwright, Page, Browser
import pandas as pd
from config import SYSTEMS_CONFIG, TEST_CONFIG, PATHS, generate_filename, generate_screenshot_name

class SystemTester:
    def __init__(self, system_key: str):
        """
        初始化系统测试器
        
        Args:
            system_key: 系统标识符 ('boss3' 或 'huoban3')
        """
        if system_key not in SYSTEMS_CONFIG:
            raise ValueError(f"不支持的系统: {system_key}")
            
        self.system_key = system_key
        self.system_config = SYSTEMS_CONFIG[system_key]
        self.test_results = []
        self.bug_reports = []
        self.function_modules = []
        self.screenshots = []
        
    async def run_comprehensive_test(self, browser_type="chromium"):
        """
        运行全面的系统测试
        
        Args:
            browser_type: 浏览器类型 ('chromium', 'firefox', 'webkit')
        """
        print(f"开始测试 {self.system_config['name']}...")
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await self._launch_browser(p, browser_type)
            page = await browser.new_page()
            
            try:
                # 1. 登录系统
                login_success = await self.test_login(page)
                if not login_success:
                    print("登录失败，无法继续测试")
                    return
                
                # 2. 发现功能模块
                await self.discover_function_modules(page)
                
                # 3. 测试每个功能模块
                for module in self.function_modules:
                    await self.test_function_module(page, module)
                
                # 4. 生成测试报告
                await self.generate_test_report()
                
            except Exception as e:
                print(f"测试过程中发生错误: {str(e)}")
                self._record_bug("SYSTEM_ERROR", "系统测试", "P0", f"测试执行异常: {str(e)}")
                
            finally:
                await browser.close()
                
        print(f"{self.system_config['name']} 测试完成!")
        
    async def _launch_browser(self, playwright, browser_type):
        """启动浏览器"""
        browser_launcher = getattr(playwright, browser_type)
        return await browser_launcher.launch(
            headless=TEST_CONFIG["headless"],
            args=['--no-sandbox', '--disable-dev-shm-usage']  # 添加这些参数以提高兼容性
        )
        
    async def test_login(self, page: Page) -> bool:
        """
        测试登录功能
        
        Returns:
            bool: 登录是否成功
        """
        try:
            print("正在测试登录功能...")
            
            # 访问登录页面
            await page.goto(self.system_config["url"], timeout=TEST_CONFIG["timeout"])
            await page.wait_for_load_state(TEST_CONFIG["wait_for_load_state"])
            
            # 截取登录页面
            screenshot_path = os.path.join(
                PATHS["screenshots"], 
                generate_screenshot_name(self.system_key, "login", "page", 1)
            )
            await page.screenshot(path=screenshot_path, full_page=TEST_CONFIG["screenshot_full_page"])
            self.screenshots.append({"type": "login_page", "path": screenshot_path})
            
            # 查找登录表单元素
            username_selector = await self._find_login_elements(page, "username")
            password_selector = await self._find_login_elements(page, "password")
            submit_selector = await self._find_login_elements(page, "submit")
            
            if not all([username_selector, password_selector, submit_selector]):
                self._record_bug("LOGIN_001", "登录功能", "P1", "无法找到登录表单元素")
                return False
            
            # 填写登录信息
            await page.fill(username_selector, self.system_config["credentials"]["username"])
            await page.fill(password_selector, self.system_config["credentials"]["password"])
            
            # 点击登录按钮
            await page.click(submit_selector)
            await page.wait_for_load_state(TEST_CONFIG["wait_for_load_state"])
            
            # 检查登录是否成功
            current_url = page.url
            if current_url != self.system_config["url"]:
                # 截取登录后页面
                screenshot_path = os.path.join(
                    PATHS["screenshots"], 
                    generate_screenshot_name(self.system_key, "login", "success", 2)
                )
                await page.screenshot(path=screenshot_path, full_page=TEST_CONFIG["screenshot_full_page"])
                self.screenshots.append({"type": "login_success", "path": screenshot_path})
                
                self._record_test_result("登录功能", "PASS", "登录成功", screenshot_path)
                return True
            else:
                self._record_bug("LOGIN_002", "登录功能", "P1", "登录失败，页面未跳转")
                return False
                
        except Exception as e:
            self._record_bug("LOGIN_003", "登录功能", "P0", f"登录过程异常: {str(e)}")
            return False
            
    async def _find_login_elements(self, page: Page, element_type: str) -> str:
        """
        查找登录页面元素
        
        Args:
            page: 页面对象
            element_type: 元素类型 ('username', 'password', 'submit')
            
        Returns:
            str: 元素选择器
        """
        selectors = {
            "username": [
                'input[name="username"]',
                'input[name="user"]', 
                'input[name="account"]',
                'input[type="text"]',
                'input[placeholder*="用户名"]',
                'input[placeholder*="账号"]'
            ],
            "password": [
                'input[name="password"]',
                'input[name="pwd"]',
                'input[type="password"]',
                'input[placeholder*="密码"]'
            ],
            "submit": [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("登录")',
                'button:has-text("登陆")',
                'button:has-text("Sign In")',
                '.login-btn',
                '.submit-btn'
            ]
        }
        
        for selector in selectors.get(element_type, []):
            try:
                element = await page.query_selector(selector)
                if element:
                    return selector
            except:
                continue
                
        return None

    async def discover_function_modules(self, page: Page):
        """
        发现系统功能模块
        """
        try:
            print("正在发现功能模块...")

            # 等待页面加载完成
            await page.wait_for_load_state(TEST_CONFIG["wait_for_load_state"])

            # 截取主页面
            screenshot_path = os.path.join(
                PATHS["screenshots"],
                generate_screenshot_name(self.system_key, "homepage", "overview", 1)
            )
            await page.screenshot(path=screenshot_path, full_page=TEST_CONFIG["screenshot_full_page"])
            self.screenshots.append({"type": "homepage", "path": screenshot_path})

            # 查找菜单元素
            menu_selectors = [
                'nav a',
                '.menu a',
                '.nav-menu a',
                '.sidebar a',
                '.navigation a',
                'ul.menu li a',
                '.nav-item a',
                '[role="menuitem"]'
            ]

            modules = []
            for selector in menu_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        text = await element.inner_text()
                        href = await element.get_attribute('href')
                        if text and text.strip() and len(text.strip()) > 1:
                            modules.append({
                                "name": text.strip(),
                                "selector": selector,
                                "href": href,
                                "element": element
                            })
                except:
                    continue

            # 去重并过滤
            seen_names = set()
            filtered_modules = []
            for module in modules:
                if module["name"] not in seen_names and not self._is_excluded_module(module["name"]):
                    seen_names.add(module["name"])
                    filtered_modules.append(module)

            self.function_modules = filtered_modules
            print(f"发现 {len(self.function_modules)} 个功能模块")

        except Exception as e:
            self._record_bug("DISCOVERY_001", "功能发现", "P2", f"功能模块发现异常: {str(e)}")

    def _is_excluded_module(self, module_name: str) -> bool:
        """
        判断是否应该排除某个模块
        """
        excluded_keywords = [
            "登出", "退出", "logout", "exit",
            "帮助", "help", "关于", "about",
            "首页", "home", "主页"
        ]
        return any(keyword in module_name.lower() for keyword in excluded_keywords)

    async def test_function_module(self, page: Page, module: Dict):
        """
        测试单个功能模块
        """
        module_name = module["name"]
        print(f"正在测试功能模块: {module_name}")

        try:
            start_time = time.time()

            # 点击功能模块
            if module.get("href") and module["href"].startswith("http"):
                await page.goto(module["href"])
            else:
                await page.click(f'text="{module_name}"')

            await page.wait_for_load_state(TEST_CONFIG["wait_for_load_state"])

            # 截取功能页面
            screenshot_path = os.path.join(
                PATHS["screenshots"],
                generate_screenshot_name(self.system_key, module_name, "overview", 1)
            )
            await page.screenshot(path=screenshot_path, full_page=TEST_CONFIG["screenshot_full_page"])

            # 检查页面是否正常加载
            page_title = await page.title()
            page_content = await page.content()

            execution_time = time.time() - start_time

            # 基本功能测试
            test_results = await self._perform_basic_function_tests(page, module_name)

            # 记录测试结果
            if test_results["success"]:
                self._record_test_result(
                    module_name,
                    "PASS",
                    f"功能正常，响应时间: {execution_time:.2f}秒",
                    screenshot_path
                )
            else:
                self._record_test_result(
                    module_name,
                    "FAIL",
                    test_results["error"],
                    screenshot_path
                )

        except Exception as e:
            self._record_bug(
                f"MODULE_{len(self.bug_reports)+1:03d}",
                module_name,
                "P2",
                f"模块测试异常: {str(e)}"
            )

    async def _perform_basic_function_tests(self, page: Page, module_name: str) -> Dict:
        """
        执行基本功能测试
        """
        try:
            # 检查页面是否包含错误信息
            error_indicators = ["error", "错误", "异常", "失败", "404", "500"]
            page_text = await page.inner_text("body")

            for indicator in error_indicators:
                if indicator in page_text.lower():
                    return {"success": False, "error": f"页面包含错误信息: {indicator}"}

            # 检查是否有表单元素
            forms = await page.query_selector_all("form")
            inputs = await page.query_selector_all("input")
            buttons = await page.query_selector_all("button")

            # 尝试基本交互
            if buttons:
                # 点击第一个非提交按钮
                for button in buttons[:3]:  # 只测试前3个按钮
                    try:
                        button_text = await button.inner_text()
                        if button_text and "删除" not in button_text and "提交" not in button_text:
                            await button.click()
                            await page.wait_for_timeout(1000)  # 等待1秒
                            break
                    except:
                        continue

            return {"success": True, "error": None}

        except Exception as e:
            return {"success": False, "error": f"基本功能测试异常: {str(e)}"}

    def _record_test_result(self, module: str, status: str, notes: str, screenshot: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "system": self.system_config["name"],
            "module": module,
            "status": status,
            "notes": notes,
            "screenshot": screenshot
        })

    def _record_bug(self, bug_id: str, module: str, severity: str, description: str):
        """记录Bug"""
        self.bug_reports.append({
            "bug_id": bug_id,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "system": self.system_config["name"],
            "module": module,
            "severity": severity,
            "description": description,
            "status": "未修复"
        })

    async def generate_test_report(self):
        """生成测试报告"""
        try:
            print("正在生成测试报告...")

            # 生成Excel格式的测试结果
            if self.test_results:
                df_results = pd.DataFrame(self.test_results)
                results_file = os.path.join(
                    PATHS["reports"],
                    generate_filename(self.system_key, "测试结果", "xlsx")
                )
                df_results.to_excel(results_file, index=False, sheet_name="测试结果")
                print(f"测试结果已保存到: {results_file}")

            # 生成Bug报告
            if self.bug_reports:
                df_bugs = pd.DataFrame(self.bug_reports)
                bugs_file = os.path.join(
                    PATHS["reports"],
                    generate_filename(self.system_key, "Bug报告", "xlsx")
                )
                df_bugs.to_excel(bugs_file, index=False, sheet_name="Bug报告")
                print(f"Bug报告已保存到: {bugs_file}")

            # 生成Markdown格式的测试报告
            await self._generate_markdown_report()

        except Exception as e:
            print(f"生成测试报告时发生错误: {str(e)}")

    async def _generate_markdown_report(self):
        """生成Markdown格式的测试报告"""
        report_content = f"""# {self.system_config['name']} 测试报告

## 测试概述

- **测试系统**: {self.system_config['name']}
- **测试地址**: {self.system_config['url']}
- **测试时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **测试账号**: {self.system_config['credentials']['username']}

## 测试统计

- **总功能模块数**: {len(self.function_modules)}
- **测试通过数**: {len([r for r in self.test_results if r['status'] == 'PASS'])}
- **测试失败数**: {len([r for r in self.test_results if r['status'] == 'FAIL'])}
- **发现Bug数**: {len(self.bug_reports)}

## 功能模块测试结果

"""

        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASS' else "❌"
            report_content += f"### {status_icon} {result['module']}\n\n"
            report_content += f"- **状态**: {result['status']}\n"
            report_content += f"- **说明**: {result['notes']}\n"
            if result['screenshot']:
                report_content += f"- **截图**: {os.path.basename(result['screenshot'])}\n"
            report_content += "\n"

        if self.bug_reports:
            report_content += "## Bug报告\n\n"
            for bug in self.bug_reports:
                report_content += f"### Bug ID: {bug['bug_id']}\n\n"
                report_content += f"- **模块**: {bug['module']}\n"
                report_content += f"- **严重程度**: {bug['severity']}\n"
                report_content += f"- **描述**: {bug['description']}\n"
                report_content += f"- **发现时间**: {bug['timestamp']}\n"
                report_content += f"- **状态**: {bug['status']}\n\n"

        report_content += """## 测试建议

1. 对于发现的Bug，建议按照严重程度优先级进行修复
2. 建议增加错误处理和用户友好的提示信息
3. 建议优化页面加载速度，提升用户体验
4. 建议定期进行回归测试，确保系统稳定性

## 附件

- 详细测试结果Excel文件
- Bug追踪Excel文件
- 功能截图文件夹

---
*本报告由自动化测试工具生成*
"""

        # 保存Markdown报告
        report_file = os.path.join(
            PATHS["reports"],
            generate_filename(self.system_key, "测试报告", "md")
        )

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"Markdown测试报告已保存到: {report_file}")

        return report_file
