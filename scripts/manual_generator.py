"""
用户手册生成器
基于测试结果生成用户友好的操作手册
"""

import os
import json
from datetime import datetime
from typing import Dict, List
from config import SYSTEMS_CONFIG, PATHS, generate_filename

class ManualGenerator:
    def __init__(self, system_key: str):
        """
        初始化手册生成器
        
        Args:
            system_key: 系统标识符
        """
        self.system_key = system_key
        self.system_config = SYSTEMS_CONFIG[system_key]
        self.manual_content = ""
        
    def generate_user_manual(self, test_results: List[Dict], screenshots: List[Dict]):
        """
        生成用户手册
        
        Args:
            test_results: 测试结果列表
            screenshots: 截图列表
        """
        print(f"正在生成 {self.system_config['name']} 用户手册...")
        
        # 生成Markdown格式手册
        markdown_content = self._generate_markdown_manual(test_results, screenshots)
        
        # 保存Markdown文件
        markdown_file = os.path.join(
            PATHS["docs"], 
            generate_filename(self.system_key, "用户手册", "md")
        )
        
        with open(markdown_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"Markdown用户手册已保存到: {markdown_file}")
        
        # 生成HTML格式手册
        html_content = self._convert_markdown_to_html(markdown_content)
        html_file = os.path.join(
            PATHS["docs"], 
            generate_filename(self.system_key, "用户手册", "html")
        )
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML用户手册已保存到: {html_file}")
        
        return markdown_file, html_file
        
    def _generate_markdown_manual(self, test_results: List[Dict], screenshots: List[Dict]) -> str:
        """生成Markdown格式的用户手册"""
        
        content = f"""# {self.system_config['name']} 用户操作手册

## 系统概述

{self.system_config['description']}

- **系统地址**: {self.system_config['url']}
- **手册版本**: v1.0
- **更新日期**: {datetime.now().strftime('%Y年%m月%d日')}

## 快速开始

### 系统要求

- **浏览器**: 推荐使用Chrome、Firefox、Safari等现代浏览器
- **网络**: 需要稳定的互联网连接
- **分辨率**: 建议屏幕分辨率不低于1024x768

### 登录系统

1. **打开浏览器**，访问系统地址：{self.system_config['url']}

2. **输入登录信息**：
   - 用户名：请使用您的账号
   - 密码：请使用您的密码

3. **点击登录按钮**完成登录

![登录页面](../screenshots/{self.system_key}_login_page_001.png)

> **注意**: 如果忘记密码，请联系系统管理员重置

## 功能模块详解

"""
        
        # 根据测试结果生成功能说明
        successful_modules = [r for r in test_results if r['status'] == 'PASS']
        
        for i, result in enumerate(successful_modules, 1):
            module_name = result['module']
            content += f"""### {i}. {module_name}

**功能说明**: {self._get_module_description(module_name)}

**操作步骤**:
1. 在主菜单中找到并点击"{module_name}"
2. 等待页面加载完成
3. 根据需要进行相应操作

"""
            
            # 添加截图（如果有的话）
            module_screenshots = [s for s in screenshots if module_name in s.get('path', '')]
            if module_screenshots:
                screenshot_path = os.path.basename(module_screenshots[0]['path'])
                content += f"![{module_name}功能截图](../screenshots/{screenshot_path})\n\n"
            
            content += f"""**注意事项**:
- 请确保网络连接稳定
- 操作前请仔细阅读页面提示
- 如遇问题请及时联系技术支持

---

"""
        
        content += """## 常见问题解答

### Q: 登录时提示用户名或密码错误怎么办？
A: 请检查用户名和密码是否正确，注意大小写。如果确认无误仍无法登录，请联系系统管理员。

### Q: 页面加载很慢或无法打开怎么办？
A: 请检查网络连接，尝试刷新页面。如果问题持续存在，可能是服务器维护，请稍后再试。

### Q: 操作过程中出现错误提示怎么办？
A: 请记录错误信息的具体内容，并联系技术支持人员协助解决。

### Q: 如何退出系统？
A: 点击页面右上角的"退出"或"登出"按钮安全退出系统。

## 技术支持

如果您在使用过程中遇到任何问题，请通过以下方式联系我们：

- **技术支持邮箱**: <EMAIL>
- **服务热线**: 400-XXX-XXXX
- **工作时间**: 周一至周五 9:00-18:00

## 附录

### 浏览器兼容性

| 浏览器 | 版本要求 | 兼容性 |
|--------|----------|--------|
| Chrome | 80+ | ✅ 完全支持 |
| Firefox | 75+ | ✅ 完全支持 |
| Safari | 13+ | ✅ 完全支持 |
| Edge | 80+ | ✅ 完全支持 |

### 系统更新日志

- **v1.0** ({datetime.now().strftime('%Y-%m-%d')}): 初始版本发布

---

*本手册基于系统自动化测试结果生成，如有疑问请联系技术支持*
"""
        
        return content
        
    def _get_module_description(self, module_name: str) -> str:
        """获取模块功能描述"""
        descriptions = {
            "首页": "系统主页，显示重要信息和快捷操作",
            "用户管理": "管理系统用户账号，包括添加、编辑、删除用户",
            "项目管理": "管理公益项目信息，跟踪项目进度",
            "财务管理": "管理资金流向，生成财务报表",
            "报告中心": "生成各类统计报告和数据分析",
            "系统设置": "配置系统参数和个人偏好设置",
            "数据统计": "查看各类数据统计图表和分析结果",
            "文档管理": "上传、下载和管理各类文档文件"
        }
        
        return descriptions.get(module_name, f"{module_name}相关功能操作")
        
    def _convert_markdown_to_html(self, markdown_content: str) -> str:
        """将Markdown转换为HTML"""
        
        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.system_config['name']} 用户操作手册</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }}
        h3 {{
            color: #2c3e50;
            margin-top: 25px;
        }}
        img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        blockquote {{
            background-color: #f8f9fa;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }}
        code {{
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }}
        .toc {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }}
        .toc ul {{
            list-style-type: none;
            padding-left: 0;
        }}
        .toc li {{
            margin: 5px 0;
        }}
        .toc a {{
            text-decoration: none;
            color: #3498db;
        }}
        .toc a:hover {{
            text-decoration: underline;
        }}
    </style>
</head>
<body>
    <div class="container">
"""
        
        # 简单的Markdown到HTML转换
        html_content = markdown_content
        
        # 转换标题
        html_content = html_content.replace('# ', '<h1>').replace('\n## ', '</h1>\n<h2>').replace('\n### ', '</h2>\n<h3>')
        
        # 转换图片
        import re
        html_content = re.sub(r'!\[(.*?)\]\((.*?)\)', r'<img src="\2" alt="\1" title="\1">', html_content)
        
        # 转换表格（简单处理）
        html_content = html_content.replace('|', '</td><td>').replace('\n', '</td></tr>\n<tr><td>')
        
        # 转换段落
        paragraphs = html_content.split('\n\n')
        html_paragraphs = []
        for p in paragraphs:
            if p.strip() and not p.startswith('<'):
                html_paragraphs.append(f'<p>{p.strip()}</p>')
            else:
                html_paragraphs.append(p)
        
        html_content = '\n\n'.join(html_paragraphs)
        
        html_template += html_content + """
    </div>
</body>
</html>"""
        
        return html_template
