"""
简化几何标注系统
仅使用红色方框、圆圈、箭头等几何图形进行标注，完全避免文字
"""

import os
import sys
import json
from datetime import datetime
from PIL import Image, ImageDraw
import glob
import math

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import PATHS, generate_filename

class SimpleGeometricAnnotation:
    def __init__(self):
        self.screenshots_dir = PATHS["screenshots"]
        self.annotated_dir = os.path.join(PATHS["screenshots"], "annotated_geometric")
        self.annotation_log = []
        
        # 创建几何标注目录
        os.makedirs(self.annotated_dir, exist_ok=True)
        
        # 标注样式配置
        self.style = {
            "primary_color": (255, 0, 0),      # 红色
            "secondary_color": (255, 100, 100), # 浅红色
            "accent_color": (0, 255, 0),       # 绿色（用于特殊标记）
            "box_width": 5,                     # 方框线宽
            "arrow_width": 8,                   # 箭头线宽
            "circle_radius": 30,                # 圆圈半径
            "small_circle_radius": 15,          # 小圆圈半径
        }
        
    def annotate_all_screenshots(self):
        """标注所有截图"""
        print("开始使用几何标注系统处理截图...")
        
        # 获取所有Boss3截图
        boss3_screenshots = glob.glob(os.path.join(self.screenshots_dir, "boss3_improved_*.png"))
        boss3_screenshots.sort()
        
        if not boss3_screenshots:
            print("❌ 未找到Boss3截图文件")
            return
        
        print(f"发现 {len(boss3_screenshots)} 张Boss3截图")
        
        # 按模块分组处理
        modules = self._group_screenshots(boss3_screenshots)
        
        total_processed = 0
        for module_key, screenshots in modules.items():
            print(f"\n📁 处理模块 {module_key}")
            processed = self._process_module(module_key, screenshots)
            total_processed += processed
        
        print(f"\n✅ 几何标注完成！总计处理 {total_processed} 张截图")
        
        # 生成标注说明文档
        self._generate_annotation_guide()
        
        return total_processed
    
    def _group_screenshots(self, screenshots):
        """按模块分组截图"""
        modules = {}
        
        for screenshot in screenshots:
            filename = os.path.basename(screenshot)
            
            if "login" in filename:
                module_key = "00_LOGIN"
            elif "homepage" in filename:
                module_key = "00_HOME"
            elif "module_" in filename:
                # 提取模块编号
                parts = filename.split("_")
                for i, part in enumerate(parts):
                    if part == "module" and i + 1 < len(parts):
                        module_key = f"{parts[i + 1]}_MODULE"
                        break
                else:
                    module_key = "99_OTHER"
            else:
                module_key = "99_OTHER"
            
            if module_key not in modules:
                modules[module_key] = []
            modules[module_key].append(screenshot)
        
        return modules
    
    def _process_module(self, module_key, screenshots):
        """处理单个模块的截图"""
        processed_count = 0
        
        for i, screenshot in enumerate(screenshots, 1):
            try:
                print(f"  📸 处理 {i}/{len(screenshots)}: {os.path.basename(screenshot)}")
                
                # 确定截图类型
                screenshot_type = self._determine_type(screenshot)
                
                # 进行几何标注
                success = self._annotate_screenshot(screenshot, module_key, i, screenshot_type)
                
                if success:
                    processed_count += 1
                    print(f"    ✅ 标注完成")
                else:
                    print(f"    ❌ 标注失败")
                    
            except Exception as e:
                print(f"    ❌ 处理失败: {str(e)}")
        
        return processed_count
    
    def _determine_type(self, screenshot_path):
        """确定截图类型"""
        filename = os.path.basename(screenshot_path).lower()
        
        if "login" in filename:
            return "LOGIN"
        elif "homepage" in filename:
            return "HOME"
        elif "btn" in filename and "before" in filename:
            return "BTN_BEFORE"
        elif "btn" in filename and "after" in filename:
            return "BTN_AFTER"
        elif "tab" in filename:
            return "TAB"
        elif "forms" in filename:
            return "FORM"
        elif "main" in filename:
            return "MAIN"
        else:
            return "GENERAL"
    
    def _annotate_screenshot(self, screenshot_path, module_key, sequence, screenshot_type):
        """标注单张截图"""
        try:
            # 加载图片
            image = Image.open(screenshot_path)
            draw = ImageDraw.Draw(image)
            width, height = image.size
            
            # 添加序号标注（左上角 - 红色圆圈）
            self._add_sequence_circle(draw, sequence, 60, 60)
            
            # 添加模块标注（右上角 - 红色方框）
            module_number = module_key.split("_")[0]
            self._add_module_square(draw, module_number, width - 80, 60)
            
            # 根据类型添加特定标注
            self._add_type_annotations(draw, width, height, screenshot_type)
            
            # 保存标注后的图片
            output_filename = f"GEO_{module_key}_{sequence:02d}_{screenshot_type}_{os.path.basename(screenshot_path)}"
            output_path = os.path.join(self.annotated_dir, output_filename)
            image.save(output_path, quality=95)
            
            # 记录日志
            self.annotation_log.append({
                "original": screenshot_path,
                "annotated": output_path,
                "module": module_key,
                "sequence": sequence,
                "type": screenshot_type,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            
            return True
            
        except Exception as e:
            print(f"      错误: {str(e)}")
            return False
    
    def _add_sequence_circle(self, draw, sequence, x, y):
        """添加序号圆圈（用点的数量表示序号）"""
        try:
            # 绘制主圆圈
            radius = self.style["circle_radius"]
            draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                        fill=self.style["primary_color"], 
                        outline=(128, 0, 0), width=4)
            
            # 在圆圈内用小点表示数字
            self._draw_dots_for_number(draw, sequence, x, y, radius - 10)
            
        except Exception as e:
            print(f"      添加序号圆圈失败: {str(e)}")
    
    def _add_module_square(self, draw, module_number, x, y):
        """添加模块方框（用线条数量表示模块号）"""
        try:
            # 绘制主方框
            size = self.style["circle_radius"] * 1.5
            draw.rectangle([x - size//2, y - size//2, x + size//2, y + size//2], 
                          fill=self.style["primary_color"], 
                          outline=(128, 0, 0), width=4)
            
            # 在方框内用线条表示数字
            self._draw_lines_for_number(draw, int(module_number) if module_number.isdigit() else 0, x, y, size//2 - 5)
            
        except Exception as e:
            print(f"      添加模块方框失败: {str(e)}")
    
    def _draw_dots_for_number(self, draw, number, center_x, center_y, radius):
        """用点的数量表示数字（1-9用点，10+用特殊图案）"""
        try:
            if number <= 9:
                # 1-9用相应数量的点
                dot_radius = 3
                if number == 1:
                    # 中心一个点
                    draw.ellipse([center_x - dot_radius, center_y - dot_radius, 
                                 center_x + dot_radius, center_y + dot_radius], 
                                fill=(255, 255, 255))
                elif number <= 6:
                    # 圆形排列
                    angle_step = 2 * math.pi / number
                    for i in range(number):
                        angle = i * angle_step
                        dot_x = center_x + radius * 0.6 * math.cos(angle)
                        dot_y = center_y + radius * 0.6 * math.sin(angle)
                        draw.ellipse([dot_x - dot_radius, dot_y - dot_radius, 
                                     dot_x + dot_radius, dot_y + dot_radius], 
                                    fill=(255, 255, 255))
                else:
                    # 7-9用中心+外圈
                    # 中心点
                    draw.ellipse([center_x - dot_radius, center_y - dot_radius, 
                                 center_x + dot_radius, center_y + dot_radius], 
                                fill=(255, 255, 255))
                    # 外圈点
                    outer_count = number - 1
                    angle_step = 2 * math.pi / outer_count
                    for i in range(outer_count):
                        angle = i * angle_step
                        dot_x = center_x + radius * 0.7 * math.cos(angle)
                        dot_y = center_y + radius * 0.7 * math.sin(angle)
                        draw.ellipse([dot_x - dot_radius, dot_y - dot_radius, 
                                     dot_x + dot_radius, dot_y + dot_radius], 
                                    fill=(255, 255, 255))
            else:
                # 10+用十字图案
                line_length = radius * 0.8
                draw.line([center_x - line_length, center_y, center_x + line_length, center_y], 
                         fill=(255, 255, 255), width=6)
                draw.line([center_x, center_y - line_length, center_x, center_y + line_length], 
                         fill=(255, 255, 255), width=6)
            
        except Exception as e:
            print(f"      绘制数字点失败: {str(e)}")
    
    def _draw_lines_for_number(self, draw, number, center_x, center_y, size):
        """用线条数量表示数字"""
        try:
            if number == 0:
                # 0用圆圈
                draw.ellipse([center_x - size//2, center_y - size//2, 
                             center_x + size//2, center_y + size//2], 
                            outline=(255, 255, 255), width=4)
            elif number <= 5:
                # 1-5用相应数量的竖线
                line_spacing = size // (number + 1)
                start_x = center_x - (number - 1) * line_spacing // 2
                for i in range(number):
                    line_x = start_x + i * line_spacing
                    draw.line([line_x, center_y - size//2, line_x, center_y + size//2], 
                             fill=(255, 255, 255), width=4)
            else:
                # 6+用网格图案
                grid_size = 3
                cell_size = size // grid_size
                for i in range(grid_size):
                    for j in range(grid_size):
                        if i * grid_size + j < number:
                            x = center_x - size//2 + j * cell_size + cell_size//2
                            y = center_y - size//2 + i * cell_size + cell_size//2
                            draw.ellipse([x - 2, y - 2, x + 2, y + 2], fill=(255, 255, 255))
            
        except Exception as e:
            print(f"      绘制数字线条失败: {str(e)}")
    
    def _add_type_annotations(self, draw, width, height, screenshot_type):
        """根据类型添加特定标注"""
        try:
            if screenshot_type == "LOGIN":
                self._annotate_login(draw, width, height)
            elif screenshot_type == "BTN_BEFORE":
                self._annotate_button_before(draw, width, height)
            elif screenshot_type == "BTN_AFTER":
                self._annotate_button_after(draw, width, height)
            elif screenshot_type == "TAB":
                self._annotate_tabs(draw, width, height)
            elif screenshot_type == "FORM":
                self._annotate_forms(draw, width, height)
            elif screenshot_type == "MAIN":
                self._annotate_main_page(draw, width, height)
            elif screenshot_type == "HOME":
                self._annotate_homepage(draw, width, height)
                
        except Exception as e:
            print(f"      添加类型标注失败: {str(e)}")
    
    def _annotate_login(self, draw, width, height):
        """标注登录页面"""
        # 用户名输入框 - 红色方框
        self._draw_highlight_box(draw, width//2 - 180, height//2 - 100, 360, 40)
        
        # 密码输入框 - 红色方框
        self._draw_highlight_box(draw, width//2 - 180, height//2 - 40, 360, 40)
        
        # 登录按钮 - 绿色方框（表示重要操作）
        self._draw_action_box(draw, width//2 - 90, height//2 + 20, 180, 50)
        
        # 添加箭头指向登录按钮
        self._draw_arrow(draw, width//2 + 150, height//2 + 45, width//2 + 90, height//2 + 45)
    
    def _annotate_button_before(self, draw, width, height):
        """标注按钮点击前"""
        # 标注按钮区域 - 绿色方框
        self._draw_action_box(draw, 100, 200, 120, 40)
        
        # 添加箭头指向按钮
        self._draw_arrow(draw, 50, 220, 100, 220)
    
    def _annotate_button_after(self, draw, width, height):
        """标注按钮点击后"""
        # 标注结果区域 - 红色方框
        self._draw_highlight_box(draw, 100, 250, width - 200, 300)
    
    def _annotate_tabs(self, draw, width, height):
        """标注选项卡"""
        # 标注选项卡区域 - 红色方框
        self._draw_highlight_box(draw, 50, 200, width - 100, 50)
        
        # 添加箭头
        self._draw_arrow(draw, width//2, 150, width//2, 200)
    
    def _annotate_forms(self, draw, width, height):
        """标注表单"""
        # 标注整个表单区域 - 红色方框
        self._draw_highlight_box(draw, 100, 280, width - 200, height - 400)
        
        # 标注几个重要输入区域 - 小圆圈
        for i in range(3):
            y_pos = 320 + i * 60
            self._draw_small_circle(draw, 120, y_pos + 17)
    
    def _annotate_main_page(self, draw, width, height):
        """标注主页面"""
        # 标注主要内容区域 - 红色方框
        self._draw_highlight_box(draw, 50, 200, width - 100, height - 300)
    
    def _annotate_homepage(self, draw, width, height):
        """标注首页"""
        # 标注导航区域 - 红色方框
        self._draw_highlight_box(draw, 50, 150, width - 100, 100)
        
        # 标注内容区域 - 红色方框
        self._draw_highlight_box(draw, 50, 280, width - 100, height - 380)
    
    def _draw_highlight_box(self, draw, x, y, width, height):
        """绘制红色高亮方框"""
        draw.rectangle([x, y, x + width, y + height], 
                      outline=self.style["primary_color"], 
                      width=self.style["box_width"])
    
    def _draw_action_box(self, draw, x, y, width, height):
        """绘制绿色操作方框（表示可点击）"""
        draw.rectangle([x, y, x + width, y + height], 
                      outline=self.style["accent_color"], 
                      width=self.style["box_width"])
    
    def _draw_small_circle(self, draw, x, y):
        """绘制小圆圈标记"""
        radius = self.style["small_circle_radius"]
        draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                    fill=self.style["primary_color"], 
                    outline=(128, 0, 0), width=2)
    
    def _draw_arrow(self, draw, x1, y1, x2, y2):
        """绘制箭头"""
        try:
            # 绘制箭头线
            draw.line([x1, y1, x2, y2], fill=self.style["primary_color"], width=self.style["arrow_width"])
            
            # 计算箭头头部
            angle = math.atan2(y2 - y1, x2 - x1)
            arrow_length = 25
            arrow_angle = math.pi / 6
            
            # 箭头的两个分支
            x3 = x2 - arrow_length * math.cos(angle - arrow_angle)
            y3 = y2 - arrow_length * math.sin(angle - arrow_angle)
            x4 = x2 - arrow_length * math.cos(angle + arrow_angle)
            y4 = y2 - arrow_length * math.sin(angle + arrow_angle)
            
            # 绘制箭头头部
            draw.line([x2, y2, x3, y3], fill=self.style["primary_color"], width=self.style["arrow_width"])
            draw.line([x2, y2, x4, y4], fill=self.style["primary_color"], width=self.style["arrow_width"])
            
        except Exception as e:
            print(f"      绘制箭头失败: {str(e)}")
    
    def _generate_annotation_guide(self):
        """生成标注说明文档"""
        print("\n生成几何标注说明文档...")
        
        guide_content = f"""# Boss3系统几何标注说明文档

## 标注系统说明

本标注系统使用纯几何图形进行标注，完全避免文字显示问题。

### 标注元素说明

#### 1. 序号标识（左上角红色圆圈）
- **圆圈内的点**: 用点的数量表示截图序号
  - 1个点 = 序号1
  - 2-6个点 = 圆形排列表示相应序号
  - 7-9个点 = 中心点+外圈点
  - 10+ = 白色十字图案

#### 2. 模块标识（右上角红色方框）
- **方框内的线条**: 用线条/图案表示模块编号
  - 0 = 圆圈
  - 1-5 = 相应数量的竖线
  - 6+ = 网格点阵

#### 3. 操作区域标注
- **红色方框**: 标注重要的操作区域
- **绿色方框**: 标注可点击的按钮或操作元素
- **小红圆圈**: 标注次要的关注点

#### 4. 指向箭头
- **红色箭头**: 指向需要特别关注的元素

### 模块编号对照

- **00**: 登录和主页功能
- **01**: 数据看板
- **02**: 收入管理
- **03**: 支出管理
- **04**: 项目管理
- **05**: 财务统计
- **06**: 票据管理
- **07**: 合作方管理
- **08**: 组织管理
- **09**: 统计报表
- **10**: 配置管理

### 截图类型标识

#### LOGIN (登录页面)
- 3个红色方框: 用户名、密码、登录按钮
- 绿色方框: 登录按钮（可点击）
- 箭头: 指向登录按钮

#### BTN_BEFORE (按钮点击前)
- 绿色方框: 待点击的按钮
- 箭头: 指向按钮

#### BTN_AFTER (按钮点击后)
- 红色方框: 操作结果区域

#### TAB (选项卡)
- 红色方框: 选项卡区域
- 箭头: 指向选项卡

#### FORM (表单)
- 大红色方框: 整个表单区域
- 小红圆圈: 重要输入字段

#### MAIN (主页面)
- 红色方框: 主要功能区域

#### HOME (系统首页)
- 2个红色方框: 导航区域和内容区域

### 颜色含义

- **红色**: 重要区域、边框、标识
- **绿色**: 可操作元素（按钮等）
- **白色**: 标识内的图案

### 使用方法

1. **识别模块**: 查看右上角方框内的图案确定模块
2. **确定序号**: 查看左上角圆圈内的点数确定顺序
3. **关注标注**: 按照红色方框查看重要区域
4. **跟随箭头**: 箭头指向的是关键操作点
5. **区分颜色**: 绿色表示可点击，红色表示重要区域

### 文件命名规则

格式: `GEO_[模块]_[序号]_[类型]_[原始文件名]`

示例:
- `GEO_01_MODULE_01_MAIN_boss3_improved_module_01_main_004_165005.png`
- `GEO_00_LOGIN_01_LOGIN_boss3_improved_login_page_001_164959.png`

### 标注统计

- **处理截图总数**: {len(self.annotation_log)}
- **标注完成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **标注方式**: 纯几何图形
- **优势**: 无字体依赖，跨平台兼容

---

**说明文档版本**: v1.0  
**生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}  
**标注系统**: 几何图形标注系统 (无文字版本)
"""
        
        # 保存说明文档
        guide_file = os.path.join(
            PATHS["docs"], 
            generate_filename("几何标注", "说明文档", "md")
        )
        
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"几何标注说明文档已保存: {guide_file}")
        
        # 保存标注日志
        log_file = os.path.join(
            PATHS["data"], 
            generate_filename("几何标注", "处理日志", "json")
        )
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotation_log, f, ensure_ascii=False, indent=2)
        
        print(f"几何标注日志已保存: {log_file}")

def main():
    """主函数"""
    print("启动几何标注系统...")
    
    annotator = SimpleGeometricAnnotation()
    total_processed = annotator.annotate_all_screenshots()
    
    print(f"\n🎉 几何标注系统处理完成！")
    print(f"📊 总计处理: {total_processed} 张截图")
    print(f"📁 输出目录: {annotator.annotated_dir}")
    print(f"🔶 标注方式: 纯几何图形（圆圈、方框、箭头）")

if __name__ == "__main__":
    main()
