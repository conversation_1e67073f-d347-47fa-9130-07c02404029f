"""
主测试运行脚本
执行两个系统的全面测试
"""

import asyncio
import sys
import os
from system_tester import SystemTester
from config import SYSTEMS_CONFIG

async def run_single_system_test(system_key: str, browser_type: str = "chromium"):
    """
    运行单个系统的测试
    
    Args:
        system_key: 系统标识符
        browser_type: 浏览器类型
    """
    print(f"\n{'='*50}")
    print(f"开始测试 {SYSTEMS_CONFIG[system_key]['name']}")
    print(f"{'='*50}")
    
    tester = SystemTester(system_key)
    await tester.run_comprehensive_test(browser_type)
    
    return tester

async def run_all_tests(browser_type: str = "chromium"):
    """
    运行所有系统的测试
    
    Args:
        browser_type: 浏览器类型
    """
    print("开始执行联劝公益基金会数字化系统全面测试")
    print(f"使用浏览器: {browser_type}")
    
    results = {}
    
    # 测试Boss3系统
    try:
        boss3_tester = await run_single_system_test("boss3", browser_type)
        results["boss3"] = boss3_tester
    except Exception as e:
        print(f"Boss3系统测试失败: {str(e)}")
        results["boss3"] = None
    
    # 测试Huoban3系统
    try:
        huoban3_tester = await run_single_system_test("huoban3", browser_type)
        results["huoban3"] = huoban3_tester
    except Exception as e:
        print(f"Huoban3系统测试失败: {str(e)}")
        results["huoban3"] = None
    
    # 生成综合报告
    await generate_comprehensive_report(results)
    
    print("\n" + "="*50)
    print("所有系统测试完成!")
    print("="*50)
    
    return results

async def generate_comprehensive_report(results: dict):
    """
    生成综合测试报告
    
    Args:
        results: 测试结果字典
    """
    print("\n正在生成综合测试报告...")
    
    from datetime import datetime
    from config import PATHS, generate_filename
    
    # 统计信息
    total_modules = 0
    total_passed = 0
    total_failed = 0
    total_bugs = 0
    
    report_content = f"""# 联劝公益基金会数字化系统综合测试报告

## 测试概述

- **测试日期**: {datetime.now().strftime('%Y年%m月%d日')}
- **测试范围**: Boss3系统 + Huoban3系统
- **测试类型**: 功能测试、界面测试、兼容性测试

## 系统测试结果汇总

"""
    
    for system_key, tester in results.items():
        if tester:
            system_name = SYSTEMS_CONFIG[system_key]['name']
            modules_count = len(tester.function_modules)
            passed_count = len([r for r in tester.test_results if r['status'] == 'PASS'])
            failed_count = len([r for r in tester.test_results if r['status'] == 'FAIL'])
            bugs_count = len(tester.bug_reports)
            
            total_modules += modules_count
            total_passed += passed_count
            total_failed += failed_count
            total_bugs += bugs_count
            
            report_content += f"""### {system_name}

- **系统地址**: {SYSTEMS_CONFIG[system_key]['url']}
- **功能模块数**: {modules_count}
- **测试通过**: {passed_count}
- **测试失败**: {failed_count}
- **发现Bug**: {bugs_count}
- **通过率**: {(passed_count/(passed_count+failed_count)*100):.1f}% (如果有测试结果)

"""
        else:
            system_name = SYSTEMS_CONFIG[system_key]['name']
            report_content += f"""### {system_name}

- **测试状态**: ❌ 测试失败，无法访问系统

"""
    
    report_content += f"""## 总体统计

- **总功能模块数**: {total_modules}
- **总测试通过数**: {total_passed}
- **总测试失败数**: {total_failed}
- **总Bug数量**: {total_bugs}
- **整体通过率**: {(total_passed/(total_passed+total_failed)*100):.1f}% (如果有测试结果)

## 测试建议

### 优先修复建议
1. **P0级别Bug**: 立即修复，影响系统核心功能
2. **P1级别Bug**: 优先修复，影响主要业务流程
3. **P2级别Bug**: 计划修复，影响用户体验
4. **P3级别Bug**: 有时间时修复，界面优化类问题

### 系统优化建议
1. **性能优化**: 优化页面加载速度，提升响应时间
2. **用户体验**: 改善界面设计，增加操作提示
3. **错误处理**: 完善错误提示信息，提供解决方案
4. **兼容性**: 确保在不同浏览器中的一致性表现

### 测试流程建议
1. **定期测试**: 建议每月进行一次全面功能测试
2. **回归测试**: 每次系统更新后进行回归测试
3. **用户验收**: 邀请实际用户参与验收测试
4. **监控机制**: 建立系统监控和告警机制

## 附件说明

- 各系统详细测试报告 (Markdown格式)
- 测试结果数据表 (Excel格式)
- Bug追踪表 (Excel格式)
- 功能截图集 (PNG格式)

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*测试工具: Playwright自动化测试框架*
"""
    
    # 保存综合报告
    report_file = os.path.join(
        PATHS["reports"], 
        generate_filename("综合", "测试报告", "md")
    )
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"综合测试报告已保存到: {report_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='联劝公益基金会数字化系统测试工具')
    parser.add_argument('--system', choices=['boss3', 'huoban3', 'all'], 
                       default='all', help='选择要测试的系统')
    parser.add_argument('--browser', choices=['chromium', 'firefox', 'webkit'], 
                       default='chromium', help='选择浏览器类型')
    
    args = parser.parse_args()
    
    if args.system == 'all':
        asyncio.run(run_all_tests(args.browser))
    else:
        asyncio.run(run_single_system_test(args.system, args.browser))

if __name__ == "__main__":
    main()
