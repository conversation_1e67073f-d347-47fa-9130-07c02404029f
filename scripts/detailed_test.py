"""
详细测试脚本
基于初步测试结果进行更深入的系统测试
"""

import asyncio
import os
import sys
import time
from datetime import datetime

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import SYSTEMS_CONFIG, PATHS, generate_filename
from playwright.async_api import async_playwright

async def detailed_system_test():
    """详细的系统测试"""
    print("开始详细系统测试...")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        # 测试Boss3系统
        await test_boss3_system(browser)
        
        # 测试Huoban3系统
        await test_huoban3_system(browser)
        
        await browser.close()

async def test_boss3_system(browser):
    """详细测试Boss3系统"""
    print("\n" + "="*50)
    print("详细测试 Boss3系统")
    print("="*50)
    
    page = await browser.new_page()
    system_config = SYSTEMS_CONFIG["boss3"]
    
    try:
        # 访问系统
        await page.goto(system_config["url"])
        await page.wait_for_load_state('domcontentloaded')
        
        # 截图登录页面
        screenshot_path = os.path.join(PATHS["screenshots"], 
                                     f"boss3_login_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        await page.screenshot(path=screenshot_path)
        print(f"登录页面截图: {screenshot_path}")
        
        # 分析页面元素
        await analyze_page_elements(page, "boss3", "登录页面")
        
        # 尝试登录
        login_success = await attempt_boss3_login(page, system_config)
        
        if login_success:
            # 探索功能模块
            await explore_boss3_functions(page)
        
    except Exception as e:
        print(f"Boss3系统测试异常: {str(e)}")
    finally:
        await page.close()

async def test_huoban3_system(browser):
    """详细测试Huoban3系统"""
    print("\n" + "="*50)
    print("详细测试 Huoban3系统")
    print("="*50)
    
    page = await browser.new_page()
    system_config = SYSTEMS_CONFIG["huoban3"]
    
    try:
        # 访问系统
        await page.goto(system_config["url"])
        await page.wait_for_load_state('domcontentloaded')
        
        # 截图登录页面
        screenshot_path = os.path.join(PATHS["screenshots"], 
                                     f"huoban3_login_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        await page.screenshot(path=screenshot_path)
        print(f"登录页面截图: {screenshot_path}")
        
        # 分析页面元素
        await analyze_page_elements(page, "huoban3", "登录页面")
        
        # 尝试登录
        login_success = await attempt_huoban3_login(page, system_config)
        
        if login_success:
            # 探索功能模块
            await explore_huoban3_functions(page)
        
    except Exception as e:
        print(f"Huoban3系统测试异常: {str(e)}")
    finally:
        await page.close()

async def analyze_page_elements(page, system_key, page_type):
    """分析页面元素"""
    print(f"\n分析{page_type}元素...")
    
    # 获取所有输入框
    inputs = await page.query_selector_all('input')
    print(f"发现 {len(inputs)} 个输入框:")
    
    for i, input_elem in enumerate(inputs):
        try:
            input_type = await input_elem.get_attribute('type') or 'text'
            input_name = await input_elem.get_attribute('name') or '未命名'
            input_id = await input_elem.get_attribute('id') or '无ID'
            placeholder = await input_elem.get_attribute('placeholder') or '无提示'
            print(f"  {i+1}. 类型:{input_type}, 名称:{input_name}, ID:{input_id}, 提示:{placeholder}")
        except:
            print(f"  {i+1}. 无法获取属性")
    
    # 获取所有按钮
    buttons = await page.query_selector_all('button, input[type="submit"]')
    print(f"\n发现 {len(buttons)} 个按钮:")
    
    for i, button in enumerate(buttons):
        try:
            button_text = await button.inner_text()
            button_type = await button.get_attribute('type') or 'button'
            print(f"  {i+1}. 文本:'{button_text}', 类型:{button_type}")
        except:
            print(f"  {i+1}. 无法获取按钮信息")

async def attempt_boss3_login(page, system_config):
    """尝试Boss3系统登录"""
    print("\n尝试Boss3系统登录...")
    
    try:
        # 查找用户名输入框 - 尝试多种选择器
        username_selectors = [
            'input[name="username"]',
            'input[name="user"]',
            'input[name="account"]',
            'input[id="username"]',
            'input[id="user"]',
            'input[placeholder*="用户名"]',
            'input[placeholder*="账号"]',
            'input[type="text"]:first-of-type'
        ]
        
        username_input = None
        for selector in username_selectors:
            try:
                username_input = await page.query_selector(selector)
                if username_input:
                    print(f"找到用户名输入框: {selector}")
                    break
            except:
                continue
        
        # 查找密码输入框
        password_input = await page.query_selector('input[type="password"]')
        if password_input:
            print("找到密码输入框")
        
        # 查找登录按钮
        login_button_selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:has-text("登录")',
            'button:has-text("登 录")',  # 注意空格
            'button:has-text("登陆")',
            'button:has-text("Sign In")',
            '.login-btn',
            '.btn-login',
            'button[type="button"]'  # 添加这个选择器
        ]
        
        login_button = None
        for selector in login_button_selectors:
            try:
                login_button = await page.query_selector(selector)
                if login_button:
                    print(f"找到登录按钮: {selector}")
                    break
            except:
                continue
        
        if username_input and password_input and login_button:
            print("开始填写登录信息...")
            
            # 清空并填写用户名
            await username_input.click()
            await username_input.fill("")
            await username_input.type(system_config["credentials"]["username"])
            
            # 清空并填写密码
            await password_input.click()
            await password_input.fill("")
            await password_input.type(system_config["credentials"]["password"])
            
            print("点击登录按钮...")
            await login_button.click()
            
            # 等待页面跳转
            await page.wait_for_load_state('domcontentloaded', timeout=10000)
            
            # 检查是否登录成功
            current_url = page.url
            if current_url != system_config["url"]:
                print("✅ Boss3登录成功!")
                
                # 截图登录后页面
                screenshot_path = os.path.join(PATHS["screenshots"], 
                                             f"boss3_after_login_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
                await page.screenshot(path=screenshot_path, full_page=True)
                print(f"登录后页面截图: {screenshot_path}")
                
                return True
            else:
                print("❌ Boss3登录失败 - 页面未跳转")
                return False
        else:
            print("❌ 无法找到完整的登录表单元素")
            print(f"用户名输入框: {'✅' if username_input else '❌'}")
            print(f"密码输入框: {'✅' if password_input else '❌'}")
            print(f"登录按钮: {'✅' if login_button else '❌'}")
            return False
            
    except Exception as e:
        print(f"❌ Boss3登录过程异常: {str(e)}")
        return False

async def attempt_huoban3_login(page, system_config):
    """尝试Huoban3系统登录"""
    print("\n尝试Huoban3系统登录...")

    try:
        # 查找用户名输入框
        username_selectors = [
            'input[name="username"]',
            'input[name="user"]',
            'input[name="account"]',
            'input[id="username"]',
            'input[id="user"]',
            'input[placeholder*="用户名"]',
            'input[placeholder*="账号"]',
            'input[placeholder*="机构"]',
            'input[type="text"]:first-of-type'
        ]

        username_input = None
        for selector in username_selectors:
            try:
                username_input = await page.query_selector(selector)
                if username_input:
                    print(f"找到用户名输入框: {selector}")
                    break
            except:
                continue

        # 查找密码输入框
        password_input = await page.query_selector('input[type="password"]')
        if password_input:
            print("找到密码输入框")

        # 查找登录按钮
        login_button_selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:has-text("登录")',
            'button:has-text("登 录")',  # 注意空格
            'button:has-text("登陆")',
            'button:has-text("Sign In")',
            '.login-btn',
            '.btn-login',
            'button[type="button"]'
        ]

        login_button = None
        for selector in login_button_selectors:
            try:
                login_button = await page.query_selector(selector)
                if login_button:
                    print(f"找到登录按钮: {selector}")
                    break
            except:
                continue

        if username_input and password_input and login_button:
            print("开始填写登录信息...")

            # 清空并填写用户名
            await username_input.click()
            await username_input.fill("")
            await username_input.type(system_config["credentials"]["username"])

            # 清空并填写密码
            await password_input.click()
            await password_input.fill("")
            await password_input.type(system_config["credentials"]["password"])

            print("点击登录按钮...")
            await login_button.click()

            # 等待页面跳转
            await page.wait_for_load_state('domcontentloaded', timeout=10000)

            # 检查是否登录成功
            current_url = page.url
            if current_url != system_config["url"]:
                print("✅ Huoban3登录成功!")

                # 截图登录后页面
                screenshot_path = os.path.join(PATHS["screenshots"],
                                             f"huoban3_after_login_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
                await page.screenshot(path=screenshot_path, full_page=True)
                print(f"登录后页面截图: {screenshot_path}")

                return True
            else:
                print("❌ Huoban3登录失败 - 页面未跳转")
                return False
        else:
            print("❌ 无法找到完整的登录表单元素")
            print(f"用户名输入框: {'✅' if username_input else '❌'}")
            print(f"密码输入框: {'✅' if password_input else '❌'}")
            print(f"登录按钮: {'✅' if login_button else '❌'}")
            return False

    except Exception as e:
        print(f"❌ Huoban3登录过程异常: {str(e)}")
        return False

async def explore_boss3_functions(page):
    """探索Boss3系统功能"""
    print("\n探索Boss3系统功能模块...")

    try:
        # 等待页面完全加载
        await page.wait_for_timeout(2000)

        # 查找菜单元素
        menu_selectors = [
            'nav a',
            '.menu a',
            '.nav-menu a',
            '.sidebar a',
            '.navigation a',
            'ul.menu li a',
            '.nav-item a',
            '[role="menuitem"]',
            '.main-menu a',
            '.left-menu a'
        ]

        all_menu_items = []
        for selector in menu_selectors:
            try:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text()
                    href = await element.get_attribute('href')
                    if text and text.strip() and len(text.strip()) > 1:
                        all_menu_items.append({
                            "text": text.strip(),
                            "href": href,
                            "selector": selector
                        })
            except:
                continue

        # 去重
        unique_items = []
        seen_texts = set()
        for item in all_menu_items:
            if item["text"] not in seen_texts:
                seen_texts.add(item["text"])
                unique_items.append(item)

        print(f"发现 {len(unique_items)} 个功能模块:")
        for i, item in enumerate(unique_items, 1):
            print(f"  {i}. {item['text']}")

        # 保存功能列表
        functions_file = os.path.join(PATHS["data"],
                                    f"boss3_functions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        with open(functions_file, 'w', encoding='utf-8') as f:
            f.write("Boss3系统功能模块列表\n")
            f.write("="*30 + "\n\n")
            for i, item in enumerate(unique_items, 1):
                f.write(f"{i}. {item['text']}\n")
                if item['href']:
                    f.write(f"   链接: {item['href']}\n")
                f.write(f"   选择器: {item['selector']}\n\n")

        print(f"功能列表已保存: {functions_file}")

    except Exception as e:
        print(f"探索Boss3功能异常: {str(e)}")

async def explore_huoban3_functions(page):
    """探索Huoban3系统功能"""
    print("\n探索Huoban3系统功能模块...")

    try:
        # 等待页面完全加载
        await page.wait_for_timeout(2000)

        # 查找菜单元素
        menu_selectors = [
            'nav a',
            '.menu a',
            '.nav-menu a',
            '.sidebar a',
            '.navigation a',
            'ul.menu li a',
            '.nav-item a',
            '[role="menuitem"]',
            '.main-menu a',
            '.left-menu a'
        ]

        all_menu_items = []
        for selector in menu_selectors:
            try:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    text = await element.inner_text()
                    href = await element.get_attribute('href')
                    if text and text.strip() and len(text.strip()) > 1:
                        all_menu_items.append({
                            "text": text.strip(),
                            "href": href,
                            "selector": selector
                        })
            except:
                continue

        # 去重
        unique_items = []
        seen_texts = set()
        for item in all_menu_items:
            if item["text"] not in seen_texts:
                seen_texts.add(item["text"])
                unique_items.append(item)

        print(f"发现 {len(unique_items)} 个功能模块:")
        for i, item in enumerate(unique_items, 1):
            print(f"  {i}. {item['text']}")

        # 保存功能列表
        functions_file = os.path.join(PATHS["data"],
                                    f"huoban3_functions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        with open(functions_file, 'w', encoding='utf-8') as f:
            f.write("Huoban3系统功能模块列表\n")
            f.write("="*30 + "\n\n")
            for i, item in enumerate(unique_items, 1):
                f.write(f"{i}. {item['text']}\n")
                if item['href']:
                    f.write(f"   链接: {item['href']}\n")
                f.write(f"   选择器: {item['selector']}\n\n")

        print(f"功能列表已保存: {functions_file}")

    except Exception as e:
        print(f"探索Huoban3功能异常: {str(e)}")

if __name__ == "__main__":
    asyncio.run(detailed_system_test())
