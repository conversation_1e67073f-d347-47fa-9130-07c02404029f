"""
使用修复后的字体处理重新生成所有标注截图
确保中文字符正常显示，提供清晰可读的操作指导
"""

import os
import sys
import json
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import glob
import platform

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import PATHS, generate_filename

class CompleteAnnotationRegenerator:
    def __init__(self):
        self.screenshots_dir = PATHS["screenshots"]
        self.original_annotated_dir = os.path.join(PATHS["screenshots"], "annotated")
        self.new_annotated_dir = os.path.join(PATHS["screenshots"], "annotated_v2")
        self.annotation_log = []

        # 创建新的标注目录
        os.makedirs(self.new_annotated_dir, exist_ok=True)

        # 标注样式配置
        self.annotation_style = {
            "box_color": (255, 0, 0),  # 红色
            "box_width": 3,
            "text_color": (255, 255, 255),  # 白色文字
            "text_bg_color": (255, 0, 0),  # 红色背景
            "font_size": 18,  # 增大字体
            "sequence_font_size": 28  # 增大序号字体
        }

        # 初始化字体
        self.fonts = self._initialize_fonts()

        # 模块名称映射
        self.module_names = {
            "login": "登录流程",
            "homepage": "主页面",
            "01": "数据看板",
            "02": "收入管理",
            "03": "支出管理",
            "04": "项目管理",
            "05": "财务统计",
            "06": "票据管理",
            "07": "合作方管理",
            "08": "组织管理",
            "09": "统计报表",
            "10": "配置管理"
        }

    def _initialize_fonts(self):
        """初始化中文字体（改进版）"""
        fonts = {}

        # 根据操作系统选择合适的中文字体路径
        system = platform.system().lower()

        font_paths = []
        if system == "linux":
            font_paths = [
                "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                "/usr/share/fonts/truetype/noto/NotoSansCJK-Bold.ttc",
                "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc",
                "/usr/share/fonts/opentype/noto/NotoSansCJK-Bold.ttc",
                "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
                "/usr/share/fonts/truetype/arphic/uming.ttc",
                "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc"
            ]
        elif system == "darwin":  # macOS
            font_paths = [
                "/System/Library/Fonts/PingFang.ttc",
                "/System/Library/Fonts/Helvetica.ttc",
                "/System/Library/Fonts/Arial.ttf"
            ]
        elif system == "windows":
            font_paths = [
                "C:/Windows/Fonts/msyhbd.ttc",     # 微软雅黑粗体
                "C:/Windows/Fonts/msyh.ttc",      # 微软雅黑
                "C:/Windows/Fonts/simhei.ttf",    # 黑体
                "C:/Windows/Fonts/simsun.ttc"     # 宋体
            ]

        # 尝试加载字体
        for size_name, size in [("normal", self.annotation_style["font_size"]),
                               ("large", self.annotation_style["sequence_font_size"])]:
            fonts[size_name] = None

            for font_path in font_paths:
                try:
                    if os.path.exists(font_path):
                        font = ImageFont.truetype(font_path, size)
                        # 测试中文字符渲染
                        test_img = Image.new('RGB', (200, 100), color='white')
                        test_draw = ImageDraw.Draw(test_img)
                        test_text = "测试中文123"
                        test_draw.text((10, 10), test_text, font=font, fill='black')

                        fonts[size_name] = font
                        print(f"✅ 成功加载字体 {size_name} ({size}px): {os.path.basename(font_path)}")
                        break
                except Exception as e:
                    continue

            # 如果没有找到合适的字体，使用默认字体
            if fonts[size_name] is None:
                try:
                    fonts[size_name] = ImageFont.load_default()
                    print(f"⚠️ 使用默认字体 {size_name}")
                except:
                    fonts[size_name] = None
                    print(f"❌ 无法加载 {size_name} 字体")

        return fonts

    def regenerate_all_annotations(self):
        """重新生成所有标注截图"""
        print("开始重新生成所有标注截图...")

        # 获取所有Boss3改进版测试的截图
        boss3_screenshots = glob.glob(os.path.join(self.screenshots_dir, "boss3_improved_*.png"))
        boss3_screenshots.sort()

        if not boss3_screenshots:
            print("❌ 未找到Boss3截图文件")
            return

        print(f"发现 {len(boss3_screenshots)} 张Boss3截图")

        # 按模块分组处理
        modules = self._group_screenshots_by_module(boss3_screenshots)

        total_processed = 0
        for module_key, module_screenshots in modules.items():
            print(f"\n📁 处理模块: {module_key} ({self.module_names.get(module_key, module_key)})")
            processed_count = self._process_module_screenshots(module_key, module_screenshots)
            total_processed += processed_count

        print(f"\n✅ 重新生成完成！总计处理 {total_processed} 张截图")

        # 生成重新标注报告
        self._generate_regeneration_report(total_processed)

    def _group_screenshots_by_module(self, screenshots):
        """按模块分组截图"""
        modules = {}

        for screenshot in screenshots:
            filename = os.path.basename(screenshot)

            # 解析模块信息
            if "module_" in filename:
                parts = filename.split("_")
                module_index = None
                for i, part in enumerate(parts):
                    if part == "module" and i + 1 < len(parts):
                        module_index = parts[i + 1]
                        break

                if module_index:
                    if module_index not in modules:
                        modules[module_index] = []
                    modules[module_index].append(screenshot)
            else:
                # 登录页面等
                if "login" in filename:
                    if "login" not in modules:
                        modules["login"] = []
                    modules["login"].append(screenshot)
                elif "homepage" in filename:
                    if "homepage" not in modules:
                        modules["homepage"] = []
                    modules["homepage"].append(screenshot)

        return modules

    def _process_module_screenshots(self, module_key, screenshots):
        """处理单个模块的截图"""
        module_name = self.module_names.get(module_key, f"模块{module_key}")
        processed_count = 0

        for i, screenshot in enumerate(screenshots, 1):
            try:
                print(f"  📸 处理截图 {i}/{len(screenshots)}: {os.path.basename(screenshot)}")

                # 确定截图类型
                screenshot_type = self._determine_screenshot_type(screenshot)

                # 重新标注截图
                success = self._annotate_screenshot(screenshot, module_key, module_name, i, screenshot_type)

                if success:
                    processed_count += 1
                    print(f"    ✅ 标注完成")
                else:
                    print(f"    ❌ 标注失败")

            except Exception as e:
                print(f"    ❌ 处理失败: {str(e)}")

        return processed_count

    def _determine_screenshot_type(self, screenshot_path):
        """确定截图类型"""
        filename = os.path.basename(screenshot_path).lower()

        if "login" in filename:
            return "login"
        elif "homepage" in filename:
            return "homepage"
        elif "btn" in filename and "before" in filename:
            return "button_before"
        elif "btn" in filename and "after" in filename:
            return "button_after"
        elif "tab" in filename:
            return "tab"
        elif "forms" in filename:
            return "forms"
        elif "main" in filename:
            return "main"
        else:
            return "general"

    def _annotate_screenshot(self, screenshot_path, module_key, module_name, sequence, screenshot_type):
        """标注单张截图"""
        try:
            # 加载图片
            image = Image.open(screenshot_path)
            draw = ImageDraw.Draw(image)
            width, height = image.size

            # 添加序号标注（左上角）
            self._add_sequence_number(draw, sequence, 40, 40)

            # 添加模块名称（右上角）
            self._add_module_name(draw, module_name, width - 200, 40)

            # 根据截图类型添加特定标注
            self._add_type_specific_annotations(draw, width, height, screenshot_type)

            # 保存标注后的图片
            annotated_filename = f"v2_{module_key}_{sequence:02d}_{screenshot_type}_{os.path.basename(screenshot_path)}"
            annotated_path = os.path.join(self.new_annotated_dir, annotated_filename)
            image.save(annotated_path, quality=95)

            # 记录标注日志
            self.annotation_log.append({
                "original": screenshot_path,
                "annotated": annotated_path,
                "module": module_name,
                "type": screenshot_type,
                "sequence": sequence,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })

            return True

        except Exception as e:
            print(f"      错误详情: {str(e)}")
            return False

    def _add_sequence_number(self, draw, sequence, x, y):
        """添加序号（改进版）"""
        try:
            font = self.fonts["large"]
            if font is None:
                return

            # 序号文字
            text = str(sequence)

            # 计算文字尺寸
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # 绘制背景圆圈（增大尺寸）
            circle_radius = max(text_width, text_height) // 2 + 20
            draw.ellipse([x - circle_radius, y - circle_radius,
                         x + circle_radius, y + circle_radius],
                        fill=self.annotation_style["text_bg_color"],
                        outline=(128, 0, 0), width=3)

            # 绘制序号文字
            draw.text((x - text_width//2, y - text_height//2), text,
                     fill=self.annotation_style["text_color"], font=font)

        except Exception as e:
            print(f"      添加序号失败: {str(e)}")

    def _add_module_name(self, draw, module_name, x, y):
        """添加模块名称（改进版）"""
        try:
            font = self.fonts["normal"]
            if font is None:
                return

            # 计算文字尺寸
            bbox = draw.textbbox((0, 0), module_name, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # 绘制背景矩形（增大内边距）
            padding = 12
            draw.rectangle([x - padding, y - padding,
                           x + text_width + padding, y + text_height + padding],
                          fill=self.annotation_style["text_bg_color"],
                          outline=(128, 0, 0), width=2)

            # 绘制文字
            draw.text((x, y), module_name,
                     fill=self.annotation_style["text_color"], font=font)

        except Exception as e:
            print(f"      添加模块名称失败: {str(e)}")

    def _add_type_specific_annotations(self, draw, width, height, screenshot_type):
        """根据截图类型添加特定标注"""
        try:
            if screenshot_type == "login":
                self._add_login_annotations(draw, width, height)
            elif screenshot_type == "button_before":
                self._add_action_label(draw, width//2, 140, "按钮点击前")
                self._add_highlight_box(draw, 100, 200, 120, 40, "待点击按钮")
            elif screenshot_type == "button_after":
                self._add_action_label(draw, width//2, 140, "按钮点击后")
                self._add_highlight_box(draw, 100, 200, width-200, 300, "操作结果区域")
            elif screenshot_type == "tab":
                self._add_action_label(draw, width//2, 140, "选项卡切换")
                self._add_highlight_box(draw, 50, 220, width - 100, 50, "选项卡区域")
            elif screenshot_type == "forms":
                self._add_action_label(draw, width//2, 140, "表单填写")
                self._add_highlight_box(draw, 100, 280, width - 200, height - 400, "表单区域")
            elif screenshot_type == "main":
                self._add_action_label(draw, width//2, 140, "功能主页面")
                self._add_highlight_box(draw, 50, 200, width - 100, height - 300, "主要功能区域")
            elif screenshot_type == "homepage":
                self._add_action_label(draw, width//2, 140, "系统主页")
                self._add_highlight_box(draw, 50, 200, width - 100, height - 300, "导航和内容区域")

        except Exception as e:
            print(f"      添加特定标注失败: {str(e)}")

    def _add_login_annotations(self, draw, width, height):
        """添加登录页面标注"""
        try:
            # 标注用户名输入框区域
            self._add_highlight_box(draw, width//2 - 180, height//2 - 100, 360, 40, "用户名输入框")

            # 标注密码输入框区域
            self._add_highlight_box(draw, width//2 - 180, height//2 - 40, 360, 40, "密码输入框")

            # 标注登录按钮区域
            self._add_highlight_box(draw, width//2 - 90, height//2 + 20, 180, 50, "登录按钮")

            # 添加操作说明
            self._add_action_label(draw, width//2, 140, "用户登录流程")

        except Exception as e:
            print(f"      添加登录标注失败: {str(e)}")

    def _add_highlight_box(self, draw, x, y, width, height, label):
        """添加高亮框（改进版）"""
        try:
            # 绘制红色边框
            draw.rectangle([x, y, x + width, y + height],
                          outline=self.annotation_style["box_color"],
                          width=self.annotation_style["box_width"])

            # 添加标签
            if label and self.fonts["normal"]:
                font = self.fonts["normal"]

                # 标签背景
                bbox = draw.textbbox((0, 0), label, font=font)
                label_width = bbox[2] - bbox[0]
                label_height = bbox[3] - bbox[1]

                # 确保标签不超出图片边界
                label_x = max(x, 15)
                label_y = max(y - label_height - 12, 15)

                # 增大标签背景
                padding = 8
                draw.rectangle([label_x - padding, label_y - padding,
                               label_x + label_width + padding, label_y + label_height + padding],
                              fill=self.annotation_style["text_bg_color"],
                              outline=(128, 0, 0), width=2)

                # 标签文字
                draw.text((label_x, label_y), label,
                         fill=self.annotation_style["text_color"], font=font)

        except Exception as e:
            print(f"      添加高亮框失败: {str(e)}")

    def _add_action_label(self, draw, x, y, action):
        """添加操作说明标签（改进版）"""
        try:
            font = self.fonts["normal"]
            if font is None:
                return

            # 计算文字尺寸
            bbox = draw.textbbox((0, 0), action, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # 绘制背景（增大尺寸）
            padding = 16
            draw.rectangle([x - text_width//2 - padding, y - padding,
                           x + text_width//2 + padding, y + text_height + padding],
                          fill=self.annotation_style["text_bg_color"],
                          outline=(128, 0, 0), width=3)

            # 绘制文字
            draw.text((x - text_width//2, y), action,
                     fill=self.annotation_style["text_color"], font=font)

        except Exception as e:
            print(f"      添加操作标签失败: {str(e)}")

    def _generate_regeneration_report(self, total_processed):
        """生成重新标注报告"""
        print("\n生成重新标注报告...")

        report_content = f"""# 截图标注重新生成报告

## 重新生成概述

- **重新生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **处理截图总数**: {total_processed}
- **字体修复**: 已解决中文字符显示问题
- **标注版本**: v2.0 (增强版)
- **输出目录**: {self.new_annotated_dir}

## 修复改进

### 字体处理改进
1. **多平台字体支持**: 自动检测操作系统并选择合适字体
2. **字体大小优化**: 增大字体尺寸提高可读性
3. **中文字符测试**: 加载字体时进行中文渲染测试
4. **降级处理**: 字体加载失败时的优雅降级

### 标注样式改进
1. **序号标注**: 增大圆圈尺寸和边框宽度
2. **模块名称**: 增大内边距和边框宽度
3. **高亮框**: 优化标签位置和背景尺寸
4. **操作说明**: 增强文字背景和边框效果

### 标注内容改进
1. **类型识别**: 智能识别截图类型并添加相应标注
2. **操作说明**: 为每种类型添加清晰的操作说明
3. **区域标注**: 精确标注关键操作区域
4. **一致性**: 保持所有标注的样式一致性

## 重新生成统计

### 按模块统计
"""

        # 统计各模块的处理情况
        module_stats = {}
        for log in self.annotation_log:
            module = log["module"]
            if module not in module_stats:
                module_stats[module] = 0
            module_stats[module] += 1

        for module, count in module_stats.items():
            report_content += f"- **{module}**: {count}张截图\n"

        report_content += f"""

### 按类型统计
"""

        # 统计各类型的处理情况
        type_stats = {}
        for log in self.annotation_log:
            screenshot_type = log["type"]
            if screenshot_type not in type_stats:
                type_stats[screenshot_type] = 0
            type_stats[screenshot_type] += 1

        type_names = {
            "login": "登录流程",
            "homepage": "主页面",
            "button_before": "按钮点击前",
            "button_after": "按钮点击后",
            "tab": "选项卡切换",
            "forms": "表单填写",
            "main": "功能主页",
            "general": "一般页面"
        }

        for screenshot_type, count in type_stats.items():
            type_name = type_names.get(screenshot_type, screenshot_type)
            report_content += f"- **{type_name}**: {count}张截图\n"

        report_content += f"""

## 质量对比

### 修复前问题
- ❌ 中文字符显示为方块或乱码
- ❌ 字体过小难以阅读
- ❌ 标注边框不够清晰
- ❌ 缺乏操作类型说明

### 修复后效果
- ✅ 中文字符正常显示
- ✅ 字体大小适中易读
- ✅ 标注边框清晰醒目
- ✅ 智能添加操作说明

## 使用建议

### 用户手册更新
1. 使用v2.0版本的标注截图替换原有截图
2. 检查所有截图的清晰度和可读性
3. 确保标注与文字说明的一致性

### 培训材料制作
1. 利用清晰的标注截图制作培训PPT
2. 按操作类型组织培训内容
3. 重点突出红色标注区域

### 质量控制
1. 定期检查标注效果
2. 收集用户反馈进行优化
3. 保持标注样式的一致性

---

**重新生成完成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
**标注版本**: v2.0 (中文字体修复版)
**处理平台**: {platform.system()} {platform.release()}
**字体状态**: {'✅ 正常' if self.fonts['normal'] else '❌ 异常'}
"""

        # 保存重新标注报告
        report_file = os.path.join(
            PATHS["reports"],
            generate_filename("截图标注", "重新生成报告", "md")
        )

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"重新标注报告已保存: {report_file}")

        # 保存标注日志
        log_file = os.path.join(
            PATHS["data"],
            generate_filename("截图标注", "重新生成日志", "json")
        )

        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotation_log, f, ensure_ascii=False, indent=2)

        print(f"重新标注日志已保存: {log_file}")

def main():
    """主函数"""
    print("开始重新生成所有标注截图...")

    regenerator = CompleteAnnotationRegenerator()
    regenerator.regenerate_all_annotations()

    print("\n🎉 所有标注截图重新生成完成!")

if __name__ == "__main__":
    main()