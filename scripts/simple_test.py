"""
简化版测试脚本
用于在浏览器依赖问题时进行基本的系统访问测试
"""

import asyncio
import os
import sys
import time
from datetime import datetime

# 添加脚本目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import SYSTEMS_CONFIG, PATHS, generate_filename

async def test_system_access():
    """测试系统访问性"""
    print("开始测试系统访问性...")
    
    try:
        from playwright.async_api import async_playwright
        
        async with async_playwright() as p:
            # 尝试启动浏览器
            try:
                browser = await p.chromium.launch(
                    headless=False,
                    args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
                )
                print("✅ 浏览器启动成功")
                
                page = await browser.new_page()
                
                # 测试Boss3系统
                await test_single_system(page, "boss3")
                
                # 测试Huoban3系统  
                await test_single_system(page, "huoban3")
                
                await browser.close()
                
            except Exception as e:
                print(f"❌ 浏览器启动失败: {str(e)}")
                print("尝试使用备用方法...")
                await test_with_requests()
                
    except ImportError:
        print("❌ Playwright未正确安装，使用备用方法...")
        await test_with_requests()

async def test_single_system(page, system_key):
    """测试单个系统"""
    system_config = SYSTEMS_CONFIG[system_key]
    print(f"\n正在测试 {system_config['name']}...")

    try:
        # 检查页面是否仍然有效
        if page.is_closed():
            print("❌ 页面已关闭，无法继续测试")
            return

        # 访问系统
        print(f"访问地址: {system_config['url']}")
        await page.goto(system_config['url'], timeout=30000)

        # 等待页面加载，使用更短的超时时间
        try:
            await page.wait_for_load_state('domcontentloaded', timeout=5000)
        except:
            print("页面加载超时，继续进行...")

        # 获取页面标题
        try:
            title = await page.title()
            print(f"页面标题: {title}")
        except:
            print("无法获取页面标题")
            title = "未知"

        # 截图
        screenshot_path = os.path.join(
            PATHS["screenshots"],
            f"{system_key}_initial_access_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        )

        try:
            await page.screenshot(path=screenshot_path, full_page=False)  # 改为非全页面截图
            print(f"截图已保存: {screenshot_path}")
        except Exception as e:
            print(f"截图失败: {str(e)}")

        # 查找登录表单
        login_elements = await find_login_elements(page)
        if login_elements:
            print("✅ 发现登录表单")
            print(f"登录元素: {login_elements}")
            await attempt_login(page, system_config, system_key)
        else:
            print("❌ 未发现登录表单")

    except Exception as e:
        print(f"❌ 访问 {system_config['name']} 失败: {str(e)}")

async def find_login_elements(page):
    """查找登录元素"""
    selectors = [
        'input[type="text"]',
        'input[type="password"]', 
        'input[name*="user"]',
        'input[name*="pass"]',
        'button[type="submit"]',
        'input[type="submit"]'
    ]
    
    found_elements = {}
    for selector in selectors:
        try:
            elements = await page.query_selector_all(selector)
            if elements:
                found_elements[selector] = len(elements)
        except:
            continue
    
    return found_elements

async def attempt_login(page, system_config, system_key):
    """尝试登录"""
    try:
        print("尝试登录...")
        
        # 查找用户名输入框
        username_selectors = [
            'input[name="username"]',
            'input[name="user"]',
            'input[type="text"]'
        ]
        
        username_input = None
        for selector in username_selectors:
            try:
                username_input = await page.query_selector(selector)
                if username_input:
                    break
            except:
                continue
        
        # 查找密码输入框
        password_input = await page.query_selector('input[type="password"]')
        
        # 查找提交按钮
        submit_selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:has-text("登录")',
            'button:has-text("登陆")'
        ]
        
        submit_button = None
        for selector in submit_selectors:
            try:
                submit_button = await page.query_selector(selector)
                if submit_button:
                    break
            except:
                continue
        
        if username_input and password_input and submit_button:
            # 填写登录信息
            await username_input.fill(system_config["credentials"]["username"])
            await password_input.fill(system_config["credentials"]["password"])
            
            # 点击登录
            await submit_button.click()
            await page.wait_for_load_state('networkidle', timeout=10000)
            
            # 检查登录结果
            current_url = page.url
            if current_url != system_config["url"]:
                print("✅ 登录成功")
                
                # 截图登录后页面
                screenshot_path = os.path.join(
                    PATHS["screenshots"], 
                    f"{system_key}_login_success_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                )
                await page.screenshot(path=screenshot_path, full_page=True)
                print(f"登录后截图: {screenshot_path}")
                
                # 探索功能菜单
                await explore_functions(page, system_key)
                
            else:
                print("❌ 登录失败")
        else:
            print("❌ 无法找到完整的登录表单")
            
    except Exception as e:
        print(f"❌ 登录过程异常: {str(e)}")

async def explore_functions(page, system_key):
    """探索功能菜单"""
    try:
        print("探索功能菜单...")
        
        # 查找菜单链接
        menu_selectors = [
            'nav a',
            '.menu a', 
            '.nav a',
            'ul li a',
            '[role="menuitem"]'
        ]
        
        all_links = []
        for selector in menu_selectors:
            try:
                links = await page.query_selector_all(selector)
                for link in links:
                    text = await link.inner_text()
                    href = await link.get_attribute('href')
                    if text and text.strip():
                        all_links.append({"text": text.strip(), "href": href})
            except:
                continue
        
        # 去重
        unique_links = []
        seen_texts = set()
        for link in all_links:
            if link["text"] not in seen_texts:
                seen_texts.add(link["text"])
                unique_links.append(link)
        
        print(f"发现 {len(unique_links)} 个菜单项:")
        for i, link in enumerate(unique_links[:10], 1):  # 只显示前10个
            print(f"  {i}. {link['text']}")
        
        # 保存菜单信息
        menu_file = os.path.join(
            PATHS["data"], 
            f"{system_key}_menu_items_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )
        
        with open(menu_file, 'w', encoding='utf-8') as f:
            f.write(f"{SYSTEMS_CONFIG[system_key]['name']} 功能菜单\n")
            f.write("="*50 + "\n\n")
            for i, link in enumerate(unique_links, 1):
                f.write(f"{i}. {link['text']}\n")
                if link['href']:
                    f.write(f"   链接: {link['href']}\n")
                f.write("\n")
        
        print(f"菜单信息已保存: {menu_file}")
        
    except Exception as e:
        print(f"❌ 探索功能菜单异常: {str(e)}")

async def test_with_requests():
    """使用requests库进行基本测试"""
    try:
        import requests
        print("使用requests进行基本连接测试...")
        
        for system_key, config in SYSTEMS_CONFIG.items():
            print(f"\n测试 {config['name']}...")
            try:
                response = requests.get(config['url'], timeout=10)
                print(f"状态码: {response.status_code}")
                print(f"响应时间: {response.elapsed.total_seconds():.2f}秒")
                
                if response.status_code == 200:
                    print("✅ 系统可访问")
                else:
                    print("❌ 系统访问异常")
                    
            except Exception as e:
                print(f"❌ 连接失败: {str(e)}")
                
    except ImportError:
        print("❌ requests库未安装，无法进行基本测试")

if __name__ == "__main__":
    asyncio.run(test_system_access())
